
"""
Controller for incident and investigation ticket management
"""
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from flask_jwt_extended import jwt_required, get_jwt, get_jwt_identity
from ..services.incident_service import IncidentService
import os
import uuid
from datetime import datetime

incident_bp = Blueprint('incident', __name__, url_prefix='/incident')

# Initialize incident service without notify_func since we use Socket.IO directly
incident_service = IncidentService()

# File upload configuration
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'log', 'csv'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_uploaded_file(file, ticket_number):
    """Save uploaded file and return file info"""
    try:
        if file and allowed_file(file.filename):
            # Create uploads directory if it doesn't exist
            upload_dir = os.path.join('uploads', 'tickets', ticket_number)
            os.makedirs(upload_dir, exist_ok=True)

            # Generate unique filename
            file_extension = file.filename.rsplit('.', 1)[1].lower()
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)

            # Save file
            file.save(file_path)

            return {
                'original_name': secure_filename(file.filename),
                'stored_name': unique_filename,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path),
                'upload_date': datetime.now().isoformat(),
                'file_type': file_extension
            }
    except Exception as e:
        print(f"❌ Error saving file: {str(e)}")
        return None

@incident_bp.route('/tickets', methods=['POST'])
@jwt_required()
def create_ticket():
    # Check if request contains files
    if request.content_type and 'multipart/form-data' in request.content_type:
        # Handle form data with files
        data = request.form.to_dict()
        files = request.files.getlist('attachments')
    else:
        # Handle JSON data (no files)
        data = request.get_json()
        files = []

    # Handle both old and new data formats
    ticket_data = {}

    # Required fields
    ticket_data['user_id'] = data.get('user_id')
    ticket_data['description'] = data.get('description')

    # Handle title/short_description
    if 'short_description' in data:
        ticket_data['short_description'] = data['short_description']
    elif 'title' in data:
        ticket_data['short_description'] = data['title']
        ticket_data['title'] = data['title']  # For backward compatibility

    # ITSM fields
    ticket_data['category'] = data.get('category', 'Software')
    ticket_data['subcategory'] = data.get('subcategory', '')
    ticket_data['impact'] = data.get('impact', 'Medium')
    ticket_data['urgency'] = data.get('urgency', 'Medium')
    ticket_data['assignment_group'] = data.get('assignment_group', '')
    ticket_data['assigned_to'] = data.get('assigned_to', '')
    ticket_data['configuration_item'] = data.get('configuration_item', '')
    ticket_data['contact_type'] = data.get('contact_type', 'Portal')
    ticket_data['location'] = data.get('location', '')

    # Backward compatibility
    if 'severity' in data:
        ticket_data['severity'] = data['severity']

    # Create ticket first
    ticket = incident_service.create_ticket(**ticket_data)
    if not ticket:
        return jsonify({'message': 'Failed to create ticket'}), 500

    # Handle file uploads if any
    attachments = []
    if files:
        for file in files:
            if file and file.filename:
                file_info = save_uploaded_file(file, ticket['ticket_number'])
                if file_info:
                    attachments.append(file_info)
                else:
                    print(f"⚠️ Failed to save file: {file.filename}")

    # Update ticket with attachments if any were uploaded
    if attachments:
        incident_service.add_attachments_to_ticket(ticket['ticket_number'], attachments, data.get('user_id'))
        # Refresh ticket data
        ticket = incident_service.get_ticket(ticket['ticket_number'])

    return jsonify(ticket), 201

@incident_bp.route('/tickets', methods=['GET'])
@jwt_required()
def list_tickets():
    # Get current user information from JWT token
    claims = get_jwt()
    user_role = claims.get('role', 'user')
    user_id = claims.get('id')

    # Admin can see all tickets, users can only see their own
    if user_role == 'admin':
        tickets = incident_service.list_tickets()
    else:
        tickets = incident_service.list_tickets_by_user(user_id)

    return jsonify(tickets)

@incident_bp.route('/tickets/<ticket_number>', methods=['GET'])
@jwt_required()
def get_ticket(ticket_number):
    # Get current user information from JWT token
    claims = get_jwt()
    user_role = claims.get('role', 'user')
    user_id = get_jwt_identity()

    ticket = incident_service.get_ticket(ticket_number)
    if not ticket:
        return jsonify({'message': 'Ticket not found'}), 404

    # Check if user has permission to view this ticket
    if user_role != 'admin' and ticket.get('user_id') != user_id:
        return jsonify({'message': 'Access denied. You can only view your own tickets.'}), 403

    return jsonify(ticket)

@incident_bp.route('/tickets/<ticket_number>', methods=['PUT'])
@jwt_required()
def update_ticket(ticket_number):
    # Get current user information from JWT token
    claims = get_jwt()
    user_role = claims.get('role', 'user')
    user_id = get_jwt_identity()

    # Check if ticket exists
    existing_ticket = incident_service.get_ticket(ticket_number)
    if not existing_ticket:
        return jsonify({'message': 'Ticket not found'}), 404

    # Check permissions: Admin can update any ticket, user can only update their own tickets
    if user_role != 'admin' and existing_ticket.get('user_id') != user_id:
        return jsonify({'message': 'Access denied. You can only update your own tickets.'}), 403

    data = request.get_json()

    # Add user_id to track who made the update
    data['updated_by'] = user_id

    ticket = incident_service.update_ticket(ticket_number, user_id, **data)
    if not ticket:
        return jsonify({'message': 'Failed to update ticket'}), 500
    return jsonify(ticket)

@incident_bp.route('/tickets/<ticket_number>/convert', methods=['POST', 'OPTIONS'])
@jwt_required(optional=True)
def convert_ticket(ticket_number):
    # Handle OPTIONS request for CORS
    if request.method == 'OPTIONS':
        return '', 200

    # Get current user information from JWT token
    claims = get_jwt()
    user_role = claims.get('role', 'user')

    # Only admins can convert tickets to incidents
    if user_role != 'admin':
        return jsonify({'message': 'Access denied. Only administrators can convert tickets to incidents.'}), 403

    try:
        incident = incident_service.convert_ticket_to_incident(ticket_number)
        if not incident:
            return jsonify({'message': 'Ticket not found'}), 404
        return jsonify(incident), 201
    except Exception as e:
        print(f"❌ Error in convert_ticket: {str(e)}")
        return jsonify({'message': f'Failed to convert ticket: {str(e)}'}), 500

@incident_bp.route('/tickets/<ticket_number>/assign', methods=['POST'])
@jwt_required()
def assign_ticket(ticket_number):
    # Get current user information from JWT token
    claims = get_jwt()
    user_role = claims.get('role', 'user')

    # Only admins can assign tickets
    if user_role != 'admin':
        return jsonify({'message': 'Access denied. Only administrators can assign tickets.'}), 403

    data = request.get_json()
    assigned_to = data.get('assigned_to')
    if not assigned_to:
        return jsonify({'message': 'Missing assigned_to'}), 400

    ticket = incident_service.assign_ticket(ticket_number, assigned_to)
    if not ticket:
        return jsonify({'message': 'Ticket not found'}), 404

    return jsonify(ticket), 200

@incident_bp.route('/tickets/<ticket_number>/attachments', methods=['POST'])
@jwt_required()
def add_ticket_attachments(ticket_number):
    """Add attachments to an existing ticket"""
    # Get current user information from JWT token
    claims = get_jwt()
    user_role = claims.get('role', 'user')
    user_id = get_jwt_identity()

    # Verify ticket exists first
    ticket = incident_service.get_ticket(ticket_number)
    if not ticket:
        return jsonify({'message': 'Ticket not found'}), 404

    # Check permissions: Admin can add to any ticket, user can only add to their own tickets
    if user_role != 'admin' and ticket.get('user_id') != user_id:
        return jsonify({'message': 'Access denied. You can only add attachments to your own tickets.'}), 403

    files = request.files.getlist('attachments')
    form_user_id = request.form.get('user_id')

    if not files:
        return jsonify({'message': 'No files provided'}), 400

    # Process file uploads
    attachments = []
    for file in files:
        if file and file.filename:
            # Check file size
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)

            if file_size > MAX_FILE_SIZE:
                return jsonify({'message': f'File {file.filename} is too large. Maximum size is 16MB'}), 400

            file_info = save_uploaded_file(file, ticket_number)
            if file_info:
                attachments.append(file_info)
            else:
                return jsonify({'message': f'Failed to save file: {file.filename}'}), 500

    if attachments:
        # Add attachments to ticket
        incident_service.add_attachments_to_ticket(ticket_number, attachments, user_id)
        # Return updated ticket
        updated_ticket = incident_service.get_ticket(ticket_number)
        return jsonify(updated_ticket), 200
    else:
        return jsonify({'message': 'No valid files were uploaded'}), 400

@incident_bp.route('/tickets/<ticket_number>/attachments/<attachment_id>', methods=['DELETE'])
@jwt_required()
def remove_ticket_attachment(ticket_number, attachment_id):
    """Remove an attachment from a ticket"""
    # Get current user information from JWT token
    claims = get_jwt()
    user_role = claims.get('role', 'user')
    user_id = get_jwt_identity()

    # Verify ticket exists
    ticket = incident_service.get_ticket(ticket_number)
    if not ticket:
        return jsonify({'message': 'Ticket not found'}), 404

    # Check permissions: Admin can remove from any ticket, user can only remove from their own tickets
    if user_role != 'admin' and ticket.get('user_id') != user_id:
        return jsonify({'message': 'Access denied. You can only remove attachments from your own tickets.'}), 403

    # Remove attachment
    success = incident_service.remove_attachment_from_ticket(ticket_number, attachment_id, user_id)
    if success:
        updated_ticket = incident_service.get_ticket(ticket_number)
        return jsonify(updated_ticket), 200
    else:
        return jsonify({'message': 'Attachment not found or could not be removed'}), 404

# Routes for incidents
@incident_bp.route('/incidents', methods=['GET'])
def list_incidents():
    incidents = incident_service.list_incidents()
    return jsonify(incidents)

@incident_bp.route('/incidents/<incident_id>', methods=['GET'])
def get_incident(incident_id):
    incident = incident_service.get_incident(incident_id)
    if not incident:
        return jsonify({'message': 'Incident not found'}), 404
    return jsonify(incident)

@incident_bp.route('/incidents/<incident_id>', methods=['PUT'])
def update_incident(incident_id):
    data = request.get_json()
    incident = incident_service.update_incident(incident_id, **data)
    if not incident:
        return jsonify({'message': 'Incident not found'}), 404
    return jsonify(incident)

# Routes for notification settings
@incident_bp.route('/notification-settings', methods=['GET'])
@jwt_required()
def get_notification_settings():
    try:
        # Récupérer les informations du JWT
        claims = get_jwt()
        user_id = get_jwt_identity()
        user_role = claims.get('role')

        print(f"🔍 GET notification-settings - User ID: {user_id}, Role: {user_role}")

        # Vérifier que l'utilisateur est admin
        if user_role != 'admin':
            return jsonify({'message': 'Admin access required'}), 403

        settings = incident_service.get_notification_settings(user_id)
        print(f"🔍 Settings retrieved: {settings}")
        return jsonify(settings if settings else {})
    except Exception as e:
        print(f"❌ Error in get_notification_settings: {str(e)}")
        return jsonify({'message': 'Internal server error'}), 500

@incident_bp.route('/notification-settings', methods=['POST'])
@jwt_required()
def create_or_update_notification_settings():
    try:
        # Récupérer les informations du JWT
        claims = get_jwt()
        user_id = get_jwt_identity()
        user_role = claims.get('role')

        print(f"🔍 POST notification-settings - User ID: {user_id}, Role: {user_role}")

        # Vérifier que l'utilisateur est admin
        if user_role != 'admin':
            return jsonify({'message': 'Admin access required'}), 403

        data = request.get_json()
        print(f"🔍 Data received: {data}")

        # Ne pas ajouter user_id dans data car il est déjà passé comme argument
        # Retirer user_id de data s'il existe pour éviter les conflits
        if 'user_id' in data:
            del data['user_id']

        settings = incident_service.create_or_update_notification_settings(user_id, data)
        print(f"🔍 Settings saved: {settings}")
        return jsonify(settings), 200
    except Exception as e:
        print(f"❌ Error in create_or_update_notification_settings: {str(e)}")
        return jsonify({'message': 'Internal server error'}), 500

# Test endpoint pour vérifier l'authentification JWT
@incident_bp.route('/test-auth', methods=['GET'])
@jwt_required()
def test_auth():
    try:
        claims = get_jwt()
        user_id = get_jwt_identity()
        user_role = claims.get('role')

        return jsonify({
            'message': 'Authentication successful',
            'user_id': user_id,
            'role': user_role,
            'claims': claims
        }), 200
    except Exception as e:
        return jsonify({'message': f'Auth test failed: {str(e)}'}), 500

# Test endpoint pour les notifications Telegram
@incident_bp.route('/test-telegram', methods=['POST'])
@jwt_required()
def test_telegram_notification():
    try:
        # Récupérer les informations du JWT
        claims = get_jwt()
        user_role = claims.get('role')

        # Vérifier que l'utilisateur est admin
        if user_role != 'admin':
            return jsonify({'message': 'Admin access required'}), 403

        # Envoyer une notification de test
        test_data = {
            'message': 'Test notification from PICA',
            'timestamp': 'now',
            'user': 'admin'
        }

        incident_service.notify_admins('test_notification', test_data)

        return jsonify({'message': 'Test notification sent successfully'}), 200
    except Exception as e:
        print(f"❌ Error in test_telegram_notification: {str(e)}")
        return jsonify({'message': 'Failed to send test notification'}), 500
