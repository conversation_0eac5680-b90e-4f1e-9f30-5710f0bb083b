"""
Service pour la gestion des incidents et des tickets d'enquête
"""
from ..models.incident_model import (
    create_ticket, get_all_tickets, get_tickets_by_user, get_ticket_by_number, update_ticket,
    create_incident, get_all_incidents, get_incident_by_incident_id, update_incident,
    get_notification_settings_by_user_id, create_or_update_notification_settings,
    get_all_notification_settings, add_timeline_entry, add_attachments_to_ticket_db,
    remove_attachment_from_ticket_db
)
from ..utils.websocket import (
    notify_new_incident, notify_new_ticket, notify_critical_alert
)
from ..extensions import socketio
from datetime import datetime
import uuid
import requests
from ..utils.email_utils import send_async_email
from .email_template_service import EmailTemplateService

class IncidentService:
    def __init__(self, notify_func=None):
        self.notify_func = notify_func

    def notify_admins(self, event_name, data):
        # Emit websocket to all connected clients
        try:
            socketio.emit('incident_notification', {
                'event': event_name,
                'data': data,
                'timestamp': datetime.now().isoformat()
            })
            print(f"✅ Socket.IO notification sent: {event_name}")
        except Exception as e:
            print(f"❌ Socket.IO notification failed: {e}")

        # Legacy websocket support
        if self.notify_func:
            self.notify_func(event_name, data)

        # Charger les admins
        print(f"🔔 notify_admins called - Event: {event_name}")
        admins = get_all_notification_settings()
        print(f"🔔 Found {len(admins) if admins else 0} admin notification settings")

        if not admins:
            print("⚠️ No admin notification settings found")
            return

        for admin in admins:
            print(f"🔔 Processing admin: {admin}")

            # Skip file-related notifications for Telegram
            if event_name in ['ticket_attachments_added', 'ticket_attachment_removed']:
                print(f"📱 Skipping Telegram notification for file event: {event_name}")
                continue

            # Format message for Telegram with better formatting
            telegram_message = self.format_telegram_message(event_name, data)

            # Send email if enabled
            if admin.get('email_enabled') and admin.get('email_address'):
                print(f"📧 Sending email to {admin['email_address']}")
                self.send_email(admin['email_address'], f"PICA - {event_name}", str(data))

            # Send telegram if enabled (using fixed configuration)
            if admin.get('telegram_enabled'):
                print(f"📱 Sending Telegram notification")
                self.send_telegram_message(admin.get('telegram_chat_id'), telegram_message)
            else:
                print(f"📱 Telegram not enabled for admin: telegram_enabled={admin.get('telegram_enabled')}")

    def send_email(self, to_email, subject, body):
        """Send email notification using beautiful templates"""
        print(f"📧 Sending EMAIL to {to_email}: Subject={subject}")

        try:
            # Determine notification type and action from subject
            if "New Ticket Created" in subject:
                notification_type = "New Ticket Created"
                item_type = "ticket"
                action = "created"
            elif "Ticket Updated" in subject:
                notification_type = "Ticket Updated"
                item_type = "ticket"
                action = "updated"
            elif "New Incident" in subject:
                notification_type = "New Incident Created"
                item_type = "incident"
                action = "created"
            else:
                notification_type = subject
                item_type = "item"
                action = "updated"

            # Send beautiful email using template
            EmailTemplateService.send_incident_notification_email(
                email=to_email,
                notification_type=notification_type,
                item_type=item_type,
                action=action,
                data=body if isinstance(body, dict) else {}
            )
            print(f"✅ Templated email sent successfully to {to_email}")

        except Exception as e:
            print(f"❌ Failed to send email to {to_email}: {str(e)}")

    def format_email_body(self, subject, data):
        """Format email body for incident notifications"""

        # Convert data to dict if it's a string representation
        if isinstance(data, str):
            try:
                import ast
                data = ast.literal_eval(data)
            except:
                # If parsing fails, use the string as is
                return f"""
Hello,

You have received a new PICA notification:

{subject}

Details: {data}

Best regards,
PICA Team
"""

        # Format based on the type of notification
        if 'ticket_number' in data:
            # Calculate criticality score (same logic as Telegram)
            impact = data.get('impact', 'Medium')
            urgency = data.get('urgency', 'Medium')
            priority = data.get('priority', 3)

            criticality_score = 50
            if impact == 'High':
                criticality_score += 25
            elif impact == 'Medium':
                criticality_score += 15

            if urgency == 'High':
                criticality_score += 25
            elif urgency == 'Medium':
                criticality_score += 15

            if priority == 1:
                criticality_score += 10
            elif priority == 2:
                criticality_score += 5

            priority_labels = {1: 'Critical', 2: 'High', 3: 'Medium', 4: 'Low'}
            priority_label = priority_labels.get(priority, 'Medium')

            from datetime import datetime
            created_date = datetime.now().strftime('%Y-%m-%d %H:%M')

            return f"""Hello,

PICA - New Ticket Created

📝 Number: {data.get('ticket_number', 'N/A')}
🖊 Description: {data.get('short_description', 'N/A')}
🗂 Category: {data.get('category', 'N/A')}
🚩 Type: {data.get('subcategory', 'Generic Incident')}
⚡ Impact: {impact}
🔥 Urgency: {urgency}
🚨 Priority: {priority_label}
🎯 Criticality Score: {criticality_score}/100
💻 Hostname: {data.get('configuration_item', 'N/A')}
🌐 IP: {data.get('location', 'N/A')}
👤 Created by: {data.get('user_id', 'N/A')}
🟩 Status: {data.get('status', 'New')}

🔗 View ticket: http://localhost:5173/incidents
🕒 Created on: {created_date}

Best regards,
PICA Team
"""
        else:
            # Format générique
            return f"""
Bonjour,

Vous avez reçu une nouvelle notification PICA :

{subject}

Détails : {str(data)}

Cordialement,
L'équipe PICA
"""

    def send_telegram_message(self, chat_id, message):
        print(f"🤖 send_telegram_message called - Chat ID: {chat_id}")
        print(f"🤖 Message: {message}")

        # Configuration Telegram (utilise le Chat ID fixe de la configuration)
        conf_via_telegram = 1
        conf_token = "**********************************************"
        conf_chat_id = "5694506830"  # Chat ID fixe de la configuration

        if conf_via_telegram != 1:
            print("📱 Telegram notifications disabled")
            return

        try:
            import urllib.parse

            # Encoder le message pour l'URL (comme urlencode en PHP)
            encoded_message = urllib.parse.quote(message)

            # Construire l'URL comme dans le code PHP
            url = f"https://api.telegram.org/bot{conf_token}/sendMessage?chat_id={conf_chat_id}&text={encoded_message}&parse_mode=HTML"

            print(f"🤖 Sending to Telegram API: {url[:100]}...")  # Afficher seulement le début de l'URL

            # Utiliser requests.get au lieu de POST (comme curl en PHP)
            response = requests.get(url, timeout=10, verify=False)  # verify=False équivaut à CURLOPT_SSL_VERIFYPEER false

            print(f"🤖 Telegram response: {response.status_code} - {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    print("✅ Telegram message sent successfully!")
                else:
                    print(f"❌ Telegram API error: {result}")
            else:
                print(f"❌ HTTP error: {response.status_code} - {response.text}")

        except Exception as e:
            print(f"❌ Telegram error: {str(e)}")

    def format_telegram_message(self, event_name, data):
        """Format notification message for Telegram with HTML formatting"""

        # Emoji mapping for different events
        event_emojis = {
            'ticket_created': '🎫',
            'ticket_updated': '📝',
            'ticket_assigned': '👤',
            'ticket_converted': '🔄',
            'incident_created': '🚨',
            'incident_updated': '📋',
            'critical_alert': '⚠️'
        }

        emoji = event_emojis.get(event_name, '📢')

        # Function to escape HTML characters for Telegram
        def escape_html(text):
            if not text:
                return 'N/A'
            return str(text).replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        if event_name == 'ticket_created':
            # Calculate criticality score based on impact and urgency
            impact = data.get('impact', 'Medium')
            urgency = data.get('urgency', 'Medium')
            priority = data.get('priority', 3)

            # Calculate criticality score
            criticality_score = 50  # Base score
            if impact == 'High':
                criticality_score += 25
            elif impact == 'Medium':
                criticality_score += 15

            if urgency == 'High':
                criticality_score += 25
            elif urgency == 'Medium':
                criticality_score += 15

            if priority == 1:
                criticality_score += 10
            elif priority == 2:
                criticality_score += 5

            # Priority label
            priority_labels = {1: 'Critical', 2: 'High', 3: 'Medium', 4: 'Low'}
            priority_label = priority_labels.get(priority, 'Medium')

            # Format creation date
            from datetime import datetime
            created_date = datetime.now().strftime('%Y-%m-%d %H:%M')

            message = f"""<b>PICA - New Ticket Created</b>

📝 <b>Number:</b> {escape_html(data.get('ticket_number', 'N/A'))}
🖊 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
🗂 <b>Category:</b> {escape_html(data.get('category', 'N/A'))}
🚩 <b>Type:</b> {escape_html(data.get('subcategory', 'Generic Incident'))}
⚡ <b>Impact:</b> {escape_html(impact)}
🔥 <b>Urgency:</b> {escape_html(urgency)}
🚨 <b>Priority:</b> {escape_html(priority_label)}
🎯 <b>Criticality Score:</b> {criticality_score}/100
💻 <b>Hostname:</b> {escape_html(data.get('configuration_item', 'N/A'))}
🌐 <b>IP:</b> {escape_html(data.get('location', 'N/A'))}
👤 <b>Created by:</b> {escape_html(data.get('user_id', 'N/A'))}
🟩 <b>Status:</b> {escape_html(data.get('status', 'New'))}

🔗 <b>View ticket:</b> http://localhost:5173/incidents
🕒 <b>Created on:</b> {created_date}
"""
        elif event_name == 'ticket_updated':
            message = f"""
{emoji} <b>PICA - Ticket Updated</b>

📋 <b>Number:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
📊 <b>Status:</b> {escape_html(data.get('status', 'N/A'))}
👤 <b>Modified by:</b> {escape_html(data.get('updated_by', 'N/A'))}

🕒 <i>Updated just now</i>
"""
        elif event_name == 'ticket_assigned':
            message = f"""
{emoji} <b>PICA - Ticket Assigned</b>

📋 <b>Number:</b> {escape_html(data.get('ticket_number', 'N/A'))}
📝 <b>Description:</b> {escape_html(data.get('short_description', 'N/A'))}
👤 <b>Assigned to:</b> {escape_html(data.get('assigned_to', 'N/A'))}
🔄 <b>Assigned by:</b> {escape_html(data.get('assigned_by', 'N/A'))}

🕒 <i>Assigned just now</i>
"""
        elif event_name == 'ticket_converted':
            message = f"""
{emoji} <b>PICA - Ticket Converted to Incident</b>

📋 <b>Ticket:</b> {escape_html(data.get('ticket_number', 'N/A'))}
🚨 <b>Incident ID:</b> {escape_html(data.get('incident_id', 'N/A'))}

🕒 <i>Converted just now</i>
"""
        elif event_name == 'test_notification':
            message = f"""
🧪 <b>PICA - Test de Notification</b>

✅ <b>Statut:</b> Configuration Telegram fonctionnelle
🤖 <b>Bot:</b> Connecté et opérationnel
📱 <b>Chat ID:</b> Configuré correctement

🕒 <i>Message de test envoyé à l'instant</i>

<i>Si vous recevez ce message, les notifications Telegram sont correctement configurées !</i>
"""
        else:
            # Format générique pour autres événements
            message = f"""
{emoji} <b>PICA - {event_name.replace('_', ' ').title()}</b>

📋 <b>Détails:</b>
{str(data)}

🕒 <i>Notification sent just now</i>
"""

        return message.strip()

    def create_ticket(self, **ticket_data):
        """Create a new ITSM-compliant ticket"""
        ticket_number = f"TCKT-{uuid.uuid4().hex[:8]}"

        # Ensure required fields
        ticket_data['ticket_number'] = ticket_number
        if 'status' not in ticket_data:
            ticket_data['status'] = 'New'

        # Handle backward compatibility
        if 'title' in ticket_data and 'short_description' not in ticket_data:
            ticket_data['short_description'] = ticket_data['title']

        ticket = create_ticket(ticket_data)
        if ticket:
            # Legacy notification system
            self.notify_admins('ticket_created', {
                'ticket_number': ticket['ticket_number'],
                'short_description': ticket.get('short_description', ''),
                'category': ticket.get('category', ''),
                'impact': ticket.get('impact', ''),
                'urgency': ticket.get('urgency', ''),
                'priority': ticket.get('priority', ''),
                'status': ticket.get('status', '')
            })

            # WebSocket notifications
            try:
                notify_new_ticket(ticket)
            except Exception as e:
                print(f"⚠️ Error sending WebSocket notification for new ticket: {str(e)}")

        return ticket



    def update_ticket(self, ticket_number, user_id=None, **kwargs):
        """Update ticket with timeline tracking"""
        ticket = update_ticket(ticket_number, kwargs, user_id)
        if ticket:
            self.notify_admins('ticket_updated', {
                'ticket_number': ticket['ticket_number'],
                'short_description': ticket.get('short_description', ''),
                'status': ticket.get('status', ''),
                'updated_by': user_id
            })
        return ticket

    def assign_ticket(self, ticket_number, assigned_to_user_id, assigning_user_id=None):
        """Assign ticket to a user with timeline tracking"""
        update_data = {
            'assigned_to': assigned_to_user_id,
            'status': 'In Progress'  # Auto-update status when assigned
        }
        ticket = update_ticket(ticket_number, update_data, assigning_user_id)

        if ticket:
            self.notify_admins('ticket_assigned', {
                'ticket_number': ticket['ticket_number'],
                'short_description': ticket.get('short_description', ''),
                'assigned_to': ticket['assigned_to'],
                'assigned_by': assigning_user_id
            })

        return ticket

    def convert_ticket_to_incident(self, ticket_number):
        """Convert a ticket to an incident"""
        try:
            ticket = get_ticket_by_number(ticket_number)
            if not ticket:
                print(f"❌ Ticket {ticket_number} not found")
                return None

            incident_id = f"INC-{uuid.uuid4().hex[:8]}"

            # Map ticket fields to incident fields
            title = ticket.get('short_description', 'Converted from ticket')
            description = ticket.get('description', '')

            # Map impact to severity
            impact_to_severity = {
                'High': 'high',
                'Medium': 'medium',
                'Low': 'low'
            }
            severity = impact_to_severity.get(ticket.get('impact', 'Medium'), 'medium')

            incident_data = {
                'title': title,
                'description': description,
                'severity': severity,
                'incident_id': incident_id,
                'status': 'open',
                'evidence': [],
                'timeline': [],
                'recommendations': [],
                'tags': [],
                'false_positive': False,
                'escalated': False
            }

            print(f"🔄 Converting ticket {ticket_number} to incident {incident_id}")
            incident = create_incident(incident_data)

            if incident:
                # Update ticket status
                update_data = {
                    'status': 'converted_to_incident',
                    'converted_incident_id': incident_id
                }
                update_ticket(ticket_number, update_data)
                print(f"✅ Ticket {ticket_number} converted to incident {incident_id}")

                # Send notification
                self.notify_admins('ticket_converted', {
                    'ticket_number': ticket['ticket_number'],
                    'incident_id': incident_id
                })

                return incident
            else:
                print(f"❌ Failed to create incident for ticket {ticket_number}")
                return None

        except Exception as e:
            print(f"❌ Error converting ticket {ticket_number} to incident: {str(e)}")
            return None

    def get_ticket(self, ticket_number):
        return get_ticket_by_number(ticket_number)

    def list_tickets(self):
        return get_all_tickets()

    def list_tickets_by_user(self, user_id):
        """Get tickets created by a specific user"""
        return get_tickets_by_user(user_id)

    def list_incidents(self):
        return get_all_incidents()

    def get_incident(self, incident_id):
        return get_incident_by_incident_id(incident_id)

    def update_incident(self, incident_id, **kwargs):
        incident = update_incident(incident_id, kwargs)
        if incident:
            self.notify_admins('incident_updated', incident)
        return incident

    def get_notification_settings(self, user_id):
        return get_notification_settings_by_user_id(user_id)

    def create_or_update_notification_settings(self, user_id, settings_data):
        return create_or_update_notification_settings(user_id, settings_data)

    def add_attachments_to_ticket(self, ticket_number, attachments, user_id=None):
        """Add attachments to a ticket with timeline tracking"""
        try:
            success = add_attachments_to_ticket_db(ticket_number, attachments)
            if success:
                # Add timeline entry
                add_timeline_entry(ticket_number, 'attachments_added', user_id, {
                    'files_count': len(attachments),
                    'files': [att['original_name'] for att in attachments]
                })

                # Notify admins
                self.notify_admins('ticket_attachments_added', {
                    'ticket_number': ticket_number,
                    'files_count': len(attachments),
                    'added_by': user_id
                })
            return success
        except Exception as e:
            print(f"❌ Error adding attachments to ticket {ticket_number}: {str(e)}")
            return False

    def remove_attachment_from_ticket(self, ticket_number, attachment_id, user_id=None):
        """Remove an attachment from a ticket with timeline tracking"""
        try:
            # Get attachment info before removal
            ticket = self.get_ticket(ticket_number)
            if not ticket or 'attachments' not in ticket:
                return False

            attachment_to_remove = None
            for att in ticket['attachments']:
                if att.get('id') == attachment_id or att.get('stored_name') == attachment_id:
                    attachment_to_remove = att
                    break

            if not attachment_to_remove:
                return False

            success = remove_attachment_from_ticket_db(ticket_number, attachment_id)
            if success:
                # Add timeline entry
                add_timeline_entry(ticket_number, 'attachment_removed', user_id, {
                    'file_name': attachment_to_remove.get('original_name', 'Unknown'),
                    'file_id': attachment_id
                })

                # Notify admins
                self.notify_admins('ticket_attachment_removed', {
                    'ticket_number': ticket_number,
                    'file_name': attachment_to_remove.get('original_name', 'Unknown'),
                    'removed_by': user_id
                })
            return success
        except Exception as e:
            print(f"❌ Error removing attachment from ticket {ticket_number}: {str(e)}")
            return False
