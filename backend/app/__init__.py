from flask import Flask
from .config import Config
import os
from .extensions import mongo, jwt, mail, security, cors, socketio
from .routes.auth_routes import auth_bp
from .routes.admin.admin_routes import admin_bp
from .routes.scan_routes import scan_bp
from .routes.phishing_routes import phishing_routes_bp
from .routes.vulnerability_routes import vulnerability_bp as vuln_bp
from .controllers.phishing_controller import phishing_bp
from .controllers.malware_controller import malware_bp
from .controllers.export_controller import export_bp
from .controllers.incident_controller import incident_bp
from .models.user_datastore_pymongo import PyMongoUserDatastore


def create_app():
    # Configure template folder for email templates
    template_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    app = Flask(__name__, template_folder=template_dir)
    app.config.from_object(Config)

    # Configuration CORS pour permettre les requêtes depuis le frontend
    cors.init_app(app, resources={
        r"/*": {
            "origins": [
                "http://localhost:3000",
                "http://localhost:5173",
                "http://localhost:5174",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:5173",
                "http://127.0.0.1:5174"
            ],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"],
            "supports_credentials": True
        }
    })

    # CORS est déjà géré par Flask-CORS ci-dessus - pas besoin de middleware manuel

    # 2. Initialise Mongo
    mongo.init_app(app)

    # 3. Initialise les autres extensions
    jwt.init_app(app)
    mail.init_app(app)

    # 3.1. Initialise Socket.IO avec CORS
    socketio.init_app(app, cors_allowed_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:5174",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174"
    ])

    # 4. Configure Flask-Security avec la base Mongo
    user_datastore = PyMongoUserDatastore(mongo.cx.get_database("pica"))
    security.init_app(app, user_datastore)
    app.user_datastore = user_datastore  # Exposé pour utilisation ailleurs

    # 5. Enregistre les blueprints
    app.register_blueprint(auth_bp, url_prefix="/auth")
    app.register_blueprint(admin_bp, url_prefix="/admin")
    app.register_blueprint(scan_bp, url_prefix="/scan")  # contient Nessus + OpenVAS
    app.register_blueprint(phishing_routes_bp, url_prefix="/api")  # contient detection phishing
    app.register_blueprint(vuln_bp, url_prefix="/api/vulnerabilities")  # contient gestion vulnérabilités
    app.register_blueprint(malware_bp, url_prefix="/api/malware")  # contient analyse de malware
    app.register_blueprint(export_bp, url_prefix="/api/export")  # contient exports de rapports
    app.register_blueprint(incident_bp, url_prefix="/api/incident")  # contient gestion des incidents
    # Register phishing blueprint directly as well for testing
    app.register_blueprint(phishing_bp, url_prefix="/api/phishing-direct")

    # 6. Importer les gestionnaires WebSocket
    from .utils import websocket

    return app
