
"""
Modèle pour la gestion des incidents et des tickets d'enquête dans MongoDB.
Fournit les fonctions CRUD pour les collections 'incidents' et 'investigation_tickets'.
"""
from datetime import datetime
from bson import ObjectId
from flask_jwt_extended import get_jwt_identity
from ..extensions import mongo


def get_tickets_collection():
    """Obtenir la collection des tickets d'enquête"""
    try:
        return mongo.db.investigation_tickets
    except Exception as e:
        print(f"❌ MongoDB tickets collection error: {str(e)}")
        return None


def get_incidents_collection():
    """Obtenir la collection des incidents"""
    try:
        return mongo.db.incidents
    except Exception as e:
        print(f"❌ MongoDB incidents collection error: {str(e)}")
        return None


def get_notification_settings_collection():
    """Obtenir la collection des paramètres de notification"""
    try:
        return mongo.db.notification_settings
    except Exception as e:
        print(f"❌ MongoDB notification settings collection error: {str(e)}")
        return None


# Priority calculation matrix
PRIORITY_MATRIX = {
    ('High', 'High'): 1,      # P1 - Critical
    ('High', 'Medium'): 2,    # P2 - High
    ('High', 'Low'): 3,       # P3 - Medium
    ('Medium', 'High'): 2,    # P2 - High
    ('Medium', 'Medium'): 3,  # P3 - Medium
    ('Medium', 'Low'): 4,     # P4 - Low
    ('Low', 'High'): 3,       # P3 - Medium
    ('Low', 'Medium'): 4,     # P4 - Low
    ('Low', 'Low'): 5,        # P5 - Planning
}

def calculate_priority(impact, urgency):
    """Calculate priority based on impact and urgency"""
    return PRIORITY_MATRIX.get((impact, urgency), 4)

def add_timeline_entry(ticket_number, action, user_id, details=None):
    """Add an entry to the ticket timeline"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return False

        timeline_entry = {
            'timestamp': datetime.now(),
            'action': action,
            'user_id': user_id,
            'details': details or {}
        }

        result = collection.update_one(
            {'ticket_number': ticket_number},
            {'$push': {'timeline': timeline_entry}}
        )

        return result.modified_count > 0

    except Exception as e:
        print(f"❌ Error adding timeline entry: {str(e)}")
        return False

# Ticket functions
def create_ticket(ticket_data):
    """Créer un nouveau ticket d'enquête avec tous les champs ITSM"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return None

        data_to_save = ticket_data.copy()

        # Calculate priority automatically
        impact = data_to_save.get('impact', 'Medium')
        urgency = data_to_save.get('urgency', 'Medium')
        data_to_save['priority'] = calculate_priority(impact, urgency)

        # Set default values
        data_to_save['status'] = data_to_save.get('status', 'New')
        data_to_save['created_at'] = datetime.now()
        data_to_save['updated_at'] = datetime.now()

        # Initialize timeline
        initial_timeline = [{
            'timestamp': datetime.now(),
            'action': 'ticket_created',
            'user_id': data_to_save.get('user_id'),
            'details': {
                'short_description': data_to_save.get('short_description'),
                'category': data_to_save.get('category'),
                'impact': impact,
                'urgency': urgency,
                'priority': data_to_save['priority']
            }
        }]
        data_to_save['timeline'] = initial_timeline

        result = collection.insert_one(data_to_save)
        print(f"✅ Ticket created with ID: {result.inserted_id}")

        # Return the created ticket
        return get_ticket_by_id(str(result.inserted_id))

    except Exception as e:
        print(f"❌ Error creating ticket: {str(e)}")
        return None


def get_all_tickets():
    """Récupérer tous les tickets"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return []

        tickets = list(collection.find().sort('created_at', -1))
        for ticket in tickets:
            ticket['id'] = str(ticket['_id'])
            ticket['_id'] = str(ticket['_id'])
            if 'created_at' in ticket:
                ticket['created_at'] = ticket['created_at'].isoformat()
            if 'updated_at' in ticket:
                ticket['updated_at'] = ticket['updated_at'].isoformat()

        return tickets

    except Exception as e:
        print(f"❌ Error retrieving tickets: {str(e)}")
        return []


def get_tickets_by_user(user_id):
    """Récupérer tous les tickets créés par un utilisateur spécifique"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return []

        # Filter tickets by user_id
        tickets = list(collection.find({'user_id': user_id}).sort('created_at', -1))
        for ticket in tickets:
            ticket['id'] = str(ticket['_id'])
            ticket['_id'] = str(ticket['_id'])
            if 'created_at' in ticket:
                ticket['created_at'] = ticket['created_at'].isoformat()
            if 'updated_at' in ticket:
                ticket['updated_at'] = ticket['updated_at'].isoformat()

        print(f"📋 Retrieved {len(tickets)} tickets for user {user_id}")
        return tickets

    except Exception as e:
        print(f"❌ Error retrieving tickets for user {user_id}: {str(e)}")
        return []


def get_ticket_by_number(ticket_number):
    """Récupérer un ticket par son numéro"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return None

        ticket = collection.find_one({'ticket_number': ticket_number})
        if ticket:
            ticket['id'] = str(ticket['_id'])
            ticket['_id'] = str(ticket['_id'])
            if 'created_at' in ticket:
                ticket['created_at'] = ticket['created_at'].isoformat()
            if 'updated_at' in ticket:
                ticket['updated_at'] = ticket['updated_at'].isoformat()

        return ticket

    except Exception as e:
        print(f"❌ Error retrieving ticket {ticket_number}: {str(e)}")
        return None


def get_ticket_by_id(ticket_id):
    """Récupérer un ticket par son ID"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return None

        if ObjectId.is_valid(ticket_id):
            ticket = collection.find_one({'_id': ObjectId(ticket_id)})
        else:
            ticket = collection.find_one({'ticket_number': ticket_id})

        if ticket:
            ticket['id'] = str(ticket['_id'])
            ticket['_id'] = str(ticket['_id'])
            if 'created_at' in ticket:
                ticket['created_at'] = ticket['created_at'].isoformat()
            if 'updated_at' in ticket:
                ticket['updated_at'] = ticket['updated_at'].isoformat()

        return ticket

    except Exception as e:
        print(f"❌ Error retrieving ticket {ticket_id}: {str(e)}")
        return None


def update_ticket(ticket_number, update_data, user_id=None):
    """Mettre à jour un ticket avec traçabilité timeline"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return None

        # Get current ticket to compare changes
        current_ticket = get_ticket_by_number(ticket_number)
        if not current_ticket:
            return None

        # Remove immutable fields from update_data
        immutable_fields = ['short_description', 'ticket_number', 'user_id', 'created_at', 'timeline']
        for field in immutable_fields:
            if field in update_data:
                print(f"⚠️ Attempted to update immutable field '{field}' - ignoring")
                del update_data[field]

        # Recalculate priority if impact or urgency changed
        if 'impact' in update_data or 'urgency' in update_data:
            impact = update_data.get('impact', current_ticket.get('impact', 'Medium'))
            urgency = update_data.get('urgency', current_ticket.get('urgency', 'Medium'))
            update_data['priority'] = calculate_priority(impact, urgency)

        update_data['updated_at'] = datetime.now()

        # Create timeline entry for the update
        timeline_entry = {
            'timestamp': datetime.now(),
            'action': 'ticket_updated',
            'user_id': user_id,
            'details': {
                'changes': {}
            }
        }

        # Track what changed (excluding system fields)
        for key, new_value in update_data.items():
            if key not in ['updated_at'] and key in current_ticket and current_ticket[key] != new_value:
                timeline_entry['details']['changes'][key] = {
                    'from': current_ticket[key],
                    'to': new_value
                }

        # Only update if there are actual changes
        if not timeline_entry['details']['changes']:
            print("ℹ️ No changes detected, skipping update")
            return current_ticket

        # Update the ticket and add timeline entry
        result = collection.update_one(
            {'ticket_number': ticket_number},
            {
                '$set': update_data,
                '$push': {'timeline': timeline_entry}
            }
        )

        if result.modified_count > 0:
            return get_ticket_by_number(ticket_number)
        return None

    except Exception as e:
        print(f"❌ Error updating ticket {ticket_number}: {str(e)}")
        return None


# Incident functions
def create_incident(incident_data):
    """Créer un nouvel incident"""
    try:
        collection = get_incidents_collection()
        if collection is None:
            return None

        data_to_save = incident_data.copy()
        data_to_save['created_at'] = datetime.now()
        data_to_save['updated_at'] = datetime.now()

        result = collection.insert_one(data_to_save)
        print(f"✅ Incident created with ID: {result.inserted_id}")

        # Return the created incident
        return get_incident_by_id(str(result.inserted_id))

    except Exception as e:
        print(f"❌ Error creating incident: {str(e)}")
        return None


def get_all_incidents():
    """Récupérer tous les incidents"""
    try:
        collection = get_incidents_collection()
        if collection is None:
            return []

        incidents = list(collection.find().sort('created_at', -1))
        for incident in incidents:
            incident['id'] = str(incident['_id'])
            incident['_id'] = str(incident['_id'])
            if 'created_at' in incident:
                incident['created_at'] = incident['created_at'].isoformat()
            if 'updated_at' in incident:
                incident['updated_at'] = incident['updated_at'].isoformat()
            if 'resolved_at' in incident and incident['resolved_at']:
                incident['resolved_at'] = incident['resolved_at'].isoformat()
            if 'closed_at' in incident and incident['closed_at']:
                incident['closed_at'] = incident['closed_at'].isoformat()

        return incidents

    except Exception as e:
        print(f"❌ Error retrieving incidents: {str(e)}")
        return []


def get_incident_by_incident_id(incident_id):
    """Récupérer un incident par son incident_id"""
    try:
        collection = get_incidents_collection()
        if collection is None:
            return None

        incident = collection.find_one({'incident_id': incident_id})
        if incident:
            incident['id'] = str(incident['_id'])
            incident['_id'] = str(incident['_id'])
            if 'created_at' in incident:
                incident['created_at'] = incident['created_at'].isoformat()
            if 'updated_at' in incident:
                incident['updated_at'] = incident['updated_at'].isoformat()
            if 'resolved_at' in incident and incident['resolved_at']:
                incident['resolved_at'] = incident['resolved_at'].isoformat()
            if 'closed_at' in incident and incident['closed_at']:
                incident['closed_at'] = incident['closed_at'].isoformat()

        return incident

    except Exception as e:
        print(f"❌ Error retrieving incident {incident_id}: {str(e)}")
        return None


def get_incident_by_id(incident_id):
    """Récupérer un incident par son ID MongoDB"""
    try:
        collection = get_incidents_collection()
        if collection is None:
            return None

        if ObjectId.is_valid(incident_id):
            incident = collection.find_one({'_id': ObjectId(incident_id)})
        else:
            incident = collection.find_one({'incident_id': incident_id})

        if incident:
            incident['id'] = str(incident['_id'])
            incident['_id'] = str(incident['_id'])
            if 'created_at' in incident:
                incident['created_at'] = incident['created_at'].isoformat()
            if 'updated_at' in incident:
                incident['updated_at'] = incident['updated_at'].isoformat()
            if 'resolved_at' in incident and incident['resolved_at']:
                incident['resolved_at'] = incident['resolved_at'].isoformat()
            if 'closed_at' in incident and incident['closed_at']:
                incident['closed_at'] = incident['closed_at'].isoformat()

        return incident

    except Exception as e:
        print(f"❌ Error retrieving incident {incident_id}: {str(e)}")
        return None


def update_incident(incident_id, update_data):
    """Mettre à jour un incident"""
    try:
        collection = get_incidents_collection()
        if collection is None:
            return None

        update_data['updated_at'] = datetime.now()

        result = collection.update_one(
            {'incident_id': incident_id},
            {'$set': update_data}
        )

        if result.modified_count > 0:
            return get_incident_by_incident_id(incident_id)
        return None

    except Exception as e:
        print(f"❌ Error updating incident {incident_id}: {str(e)}")
        return None


# Notification settings functions
def get_notification_settings_by_user_id(user_id):
    """Récupérer les paramètres de notification d'un utilisateur"""
    try:
        collection = get_notification_settings_collection()
        if collection is None:
            return None

        settings = collection.find_one({'user_id': user_id})
        if settings:
            settings['id'] = str(settings['_id'])
            settings['_id'] = str(settings['_id'])

        return settings

    except Exception as e:
        print(f"❌ Error retrieving notification settings for user {user_id}: {str(e)}")
        return None


def create_or_update_notification_settings(user_id, settings_data):
    """Créer ou mettre à jour les paramètres de notification"""
    try:
        collection = get_notification_settings_collection()
        if collection is None:
            return None

        settings_data['user_id'] = user_id

        collection.update_one(
            {'user_id': user_id},
            {'$set': settings_data},
            upsert=True
        )

        return get_notification_settings_by_user_id(user_id)

    except Exception as e:
        print(f"❌ Error updating notification settings for user {user_id}: {str(e)}")
        return None


def get_all_notification_settings():
    """Récupérer tous les paramètres de notification (pour les admins)"""
    try:
        collection = get_notification_settings_collection()
        if collection is None:
            return []

        settings_list = list(collection.find())
        for settings in settings_list:
            settings['id'] = str(settings['_id'])
            settings['_id'] = str(settings['_id'])

        return settings_list

    except Exception as e:
        print(f"❌ Error retrieving all notification settings: {str(e)}")
        return []


# Attachment management functions
def add_attachments_to_ticket_db(ticket_number, attachments):
    """Add attachments to a ticket in the database"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return False

        # Add unique IDs to attachments
        for attachment in attachments:
            attachment['id'] = str(ObjectId())

        result = collection.update_one(
            {'ticket_number': ticket_number},
            {
                '$push': {'attachments': {'$each': attachments}},
                '$set': {'updated_at': datetime.now()}
            }
        )

        return result.modified_count > 0

    except Exception as e:
        print(f"❌ Error adding attachments to ticket {ticket_number}: {str(e)}")
        return False


def remove_attachment_from_ticket_db(ticket_number, attachment_id):
    """Remove an attachment from a ticket in the database"""
    try:
        collection = get_tickets_collection()
        if collection is None:
            return False

        # Remove attachment by ID or stored_name
        result = collection.update_one(
            {'ticket_number': ticket_number},
            {
                '$pull': {
                    'attachments': {
                        '$or': [
                            {'id': attachment_id},
                            {'stored_name': attachment_id}
                        ]
                    }
                },
                '$set': {'updated_at': datetime.now()}
            }
        )

        return result.modified_count > 0

    except Exception as e:
        print(f"❌ Error removing attachment from ticket {ticket_number}: {str(e)}")
        return False
