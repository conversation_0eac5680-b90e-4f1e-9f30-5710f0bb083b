from datetime import datetime
from bson import ObjectId
from flask_jwt_extended import get_jwt_identity
from app.extensions import mongo


def get_scans_collection():
    """Obtenir la collection des scans"""
    try:
        return mongo.db.scans  # Utiliser la collection 'scans' comme demandé
    except Exception as e:
        print(f"❌ MongoDB collection error: {str(e)}")
        return None


def save_scan_to_db(scan_data):
    """Sauvegarder un scan dans MongoDB"""
    try:
        collection = get_scans_collection()
        if collection is None:  # Correction: utiliser 'is None' au lieu de 'not collection'
            print("❌ MongoDB collection is None")
            return None

        data_to_save = scan_data.copy()
        data_to_save['created_at'] = datetime.utcnow()
        data_to_save['user_id'] = get_jwt_identity()

        for key in ['mongo_id', '_id']:
            data_to_save.pop(key, None)

        result = collection.insert_one(data_to_save)
        print(f"✅ Scan saved to MongoDB with ID: {result.inserted_id}")
        return str(result.inserted_id)

    except Exception as e:
        print(f"❌ Error saving scan to MongoDB: {str(e)}")
        return None


def get_scans_from_db(limit=50, user_id=None):
    """Récupérer les scans depuis MongoDB"""
    try:
        collection = get_scans_collection()
        if collection is None:  # Correction: utiliser 'is None'
            print("❌ MongoDB collection is None")
            return []

        # Construire la requête de filtrage
        query = {}
        if user_id:
            query['user_id'] = user_id
            print(f"🔍 Filtering scans for user: {user_id}")
        else:
            print("🔍 Retrieving all scans (admin access)")

        # Trier par start_time (plus récent en premier), puis par created_at si disponible
        scans = list(collection.find(query).sort([('start_time', -1), ('created_at', -1)]).limit(limit))
        for scan in scans:
            scan['_id'] = str(scan['_id'])
            if 'created_at' in scan:
                scan['created_at'] = scan['created_at'].isoformat()
        print(f"✅ Retrieved {len(scans)} scans from MongoDB")
        return scans

    except Exception as e:
        print(f"❌ Error retrieving scans from MongoDB: {str(e)}")
        return []


def get_scan_by_id(scan_id, user_id=None):
    """Récupérer un scan spécifique par ID"""
    try:
        collection = get_scans_collection()
        if collection is None:  # Correction: utiliser 'is None'
            print("❌ MongoDB collection is None")
            return None

        query = {'$or': [{'scan_id': scan_id}]}
        if ObjectId.is_valid(scan_id):
            query['$or'].append({'_id': ObjectId(scan_id)})

        # Ajouter le filtre utilisateur si spécifié (non-admin)
        if user_id:
            query['user_id'] = user_id
            print(f"🔍 Filtering scan {scan_id} for user: {user_id}")

        scan = collection.find_one(query)

        if scan:
            scan['_id'] = str(scan['_id'])
            if 'created_at' in scan:
                scan['created_at'] = scan['created_at'].isoformat()
            print(f"✅ Retrieved scan {scan_id} from MongoDB")
            return scan
        else:
            print(f"❌ Scan {scan_id} not found in MongoDB collection or access denied")
            return None

    except Exception as e:
        print(f"❌ Error retrieving scan {scan_id} from MongoDB: {str(e)}")
        return None
