import axios from 'axios';
import { getToken, isTokenExpired, clearAuthData, refreshAccessToken } from '../utils/auth';

export const api = axios.create({
  baseURL: 'http://localhost:5000/',
  headers: { 'Content-Type': 'application/json' },
});

// Intercepteur de requête pour ajouter automatiquement le token
api.interceptors.request.use(
  async (config) => {
    let token = getToken();

    if (token) {
      // Vérifier si le token n'est pas expiré avant de l'envoyer
      if (!isTokenExpired(token)) {
        config.headers.Authorization = `Bearer ${token}`;
      } else {
        // Token expiré, essayer de le rafraîchir
        console.log('🔄 Token expired during request, attempting refresh...');
        const refreshResult = await refreshAccessToken();

        if (refreshResult.success && refreshResult.token) {
          // Utiliser le nouveau token
          config.headers.Authorization = `Bear<PERSON> ${refreshResult.token}`;
          console.log('✅ Token refreshed successfully for request');
        } else {
          // Refresh failed, clean up and redirect
          console.log('🔒 Token refresh failed during request, clearing auth data');
          clearAuthData();
          window.location.href = '/login';
          return Promise.reject(new Error('Token expired and refresh failed'));
        }
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle authentication errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 (Unauthorized) and 422 (Invalid Token) errors
    if (error.response && (error.response.status === 401 || error.response.status === 422)) {
      // Avoid infinite retry loops
      if (!originalRequest._retry) {
        originalRequest._retry = true;

        console.log('🔄 Authentication failed, attempting token refresh...');
        const refreshResult = await refreshAccessToken();

        if (refreshResult.success && refreshResult.token) {
          // Mettre à jour le header Authorization et réessayer la requête
          originalRequest.headers.Authorization = `Bearer ${refreshResult.token}`;
          console.log('✅ Token refreshed, retrying original request');
          return api(originalRequest);
        }
      }

      // Si le refresh a échoué ou si c'est un retry, nettoyer et rediriger
      console.log('🔒 Authentication failed and refresh unsuccessful, clearing auth data');
      clearAuthData();

      // Éviter les redirections multiples
      if (!window.location.pathname.includes('/login') && !window.location.pathname.includes('/auth')) {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Fonction pour obtenir le token JWT depuis les cookies (utilise la fonction auth utils)
const getTokenFromCookies = () => {
  const cookies = document.cookie.split(';');
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'token') {
      return value;
    }
  }
  return null;
};

// Configuration des headers avec token et vérification d'expiration
const getAuthHeaders = async () => {
  let token = getToken();

  // Vérifier si le token est expiré
  if (token && isTokenExpired(token)) {
    console.log('🔄 Token expired in getAuthHeaders, attempting refresh...');
    const refreshResult = await refreshAccessToken();

    if (refreshResult.success && refreshResult.token) {
      token = refreshResult.token;
      console.log('✅ Token refreshed successfully in getAuthHeaders');
    } else {
      console.log('🔒 Token refresh failed in getAuthHeaders, clearing auth data');
      clearAuthData();
      window.location.href = '/login';
      throw new Error('Token expired and refresh failed');
    }
  }

  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

// API_BASE_URL pour les appels directs
const API_BASE_URL = 'http://localhost:5000';

// Utility function to handle responses and authentication errors
const handleApiResponse = async (response: Response, originalUrl?: string, originalOptions?: RequestInit) => {
  // Check for authentication errors
  if (response.status === 401 || response.status === 422) {
    console.log('🔄 Authentication error in API response, attempting token refresh...');
    const refreshResult = await refreshAccessToken();

    if (refreshResult.success && refreshResult.token && originalUrl && originalOptions) {
      // Réessayer la requête avec le nouveau token
      const newHeaders = {
        ...originalOptions.headers,
        'Authorization': `Bearer ${refreshResult.token}`,
      };

      const retryResponse = await fetch(originalUrl, {
        ...originalOptions,
        headers: newHeaders,
      });

      if (retryResponse.ok) {
        console.log('✅ Request succeeded after token refresh');
        return retryResponse.json();
      }
    }

    // Si le refresh a échoué ou la requête retry a échoué
    console.log('🔒 Authentication error and refresh failed, clearing auth data');
    clearAuthData();

    // Éviter les redirections multiples
    if (!window.location.pathname.includes('/login') && !window.location.pathname.includes('/auth')) {
      window.location.href = '/login';
    }
    throw new Error('Authentication failed');
  }

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Récupérer les détails complets d'un scan avec statut des outils
export const getScanDetailsWithTools = async (scanId: string) => {
  const url = `${API_BASE_URL}/scan/pentesting/scan/details/${scanId}`;
  const options = {
    method: 'GET',
    headers: await getAuthHeaders(),
  };

  const response = await fetch(url, options);
  return handleApiResponse(response, url, options);
};

// Récupérer tous les scans en cours
export const getRunningScans = async () => {
  const url = `${API_BASE_URL}/scan/pentesting/scans/running`;
  const options = {
    method: 'GET',
    headers: await getAuthHeaders(),
  };

  const response = await fetch(url, options);
  return handleApiResponse(response, url, options);
};

// Récupérer la progression d'un scan
export const getScanProgress = async (scanId: string) => {
  const url = `${API_BASE_URL}/scan/pentesting/scan/progress/${scanId}`;
  const options = {
    method: 'GET',
    headers: await getAuthHeaders(),
  };

  const response = await fetch(url, options);
  return handleApiResponse(response, url, options);
};

// Récupérer les détails d'un scan (fonction existante)
export const getScanDetails = async (scanId: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/${scanId}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Arrêter un scan en cours
export const stopScan = async (scanId: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/stop/${scanId}`, {
    method: 'POST',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Annuler un scan
export const cancelScan = async (scanId: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/cancel/${scanId}`, {
    method: 'POST',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Redémarrer un scan
export const restartScan = async (scanId: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/restart/${scanId}`, {
    method: 'POST',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Supprimer un scan
export const deleteScan = async (scanId: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/delete/${scanId}`, {
    method: 'DELETE',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Récupérer les logs d'un scan
export const getScanLogs = async (scanId: string, limit: number = 100, level?: string, tool?: string) => {
  const params = new URLSearchParams();
  params.append('limit', limit.toString());
  if (level) params.append('level', level);
  if (tool) params.append('tool', tool);

  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/logs/${scanId}?${params}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Récupérer les logs en temps réel d'un scan
export const getLiveScanLogs = async (scanId: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/logs/${scanId}/live`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// ============================================================================
// ROBUST SCAN MANAGEMENT APIs
// ============================================================================

// Start robust web scan (survives page refreshes)
export const startRobustWebScan = async (targetUrl: string, scanType: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/web-robust`, {
    method: 'POST',
    headers: await getAuthHeaders(),
    body: JSON.stringify({
      target_url: targetUrl,
      scan_type: scanType
    })
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Get robust scan status
export const getRobustScanStatus = async (scanId: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/robust-status/${scanId}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Stop robust scan
export const stopRobustScan = async (scanId: string) => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/robust-stop/${scanId}`, {
    method: 'POST',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Get all active robust scans
export const getActiveRobustScans = async () => {
  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/robust-active`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Get robust scan logs (real-time)
export const getRobustScanLogs = async (scanId: string, limit: number = 100, level?: string, tool?: string) => {
  const params = new URLSearchParams();
  params.append('limit', limit.toString());
  if (level) params.append('level', level);
  if (tool) params.append('tool', tool);

  const response = await fetch(`${API_BASE_URL}/scan/pentesting/scan/robust-logs/${scanId}?${params}`, {
    method: 'GET',
    headers: await getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};
