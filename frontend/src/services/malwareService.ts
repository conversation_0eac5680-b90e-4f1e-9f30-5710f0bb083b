import { api } from './api';

// Types pour les analyses de malware
export interface MalwareAnalysis {
  _id?: string;
  mongo_id?: string;
  filename: string;
  original_filename: string;
  file_size: number;
  analysis_timestamp: string;
  user_id?: string;
  analysis_type?: string;
  threat_level: 'CLEAN' | 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | 'WHITELISTED';
  confidence_pct: number;
  malware_score: number;
  max_score: number;
  is_malware: boolean;
  file_info: {
    filename: string;
    file_type: string;
    hashes: {
      md5?: string;
      sha1?: string;
      sha256?: string;
      ssdeep?: string;
    };
  };
  analysis_details: {
    sections: Array<{
      name: string;
      virtual_address?: string;
      virtual_size?: number;
      raw_size?: number;
      entropy: number;
      suspicious: boolean;
      reasons: string[];
    }>;
    suspicious_strings: Array<{
      string: string;
      type: string;
    }>;
    packing_analysis: {
      status: string;
      is_packed: boolean;
      packer_name?: string;
      confidence?: string;
      indicators: string[];
    };
  };
  suspicious_apis: Array<{
    dll: string;
    function: string;
    threat_score: number;
    description: string;
  }>;
  yara_matches: Array<{
    rule: string;
    namespace?: string;
    tags: string[];
    meta: Record<string, any>;
  }>;
  iocs: {
    ips: string[];
    domains: string[];
    urls: string[];
    emails: string[];
  };
  detection_details: string[];
  recommendations: string[];
  created_at?: string;
  updated_at?: string;
}

export interface PackingAnalysis {
  status: 'DETECTED' | 'NOT DETECTED' | 'WHITELISTED';
  is_whitelisted: boolean;
  is_packed: boolean;
  confidence: string;
  packer_name?: string;
  indicators: string[];
  sections: Array<{
    name: string;
    size: number;
    entropy: number;
    executable: boolean;
    suspicious: boolean;
  }>;
  suspicious_imports: Array<{
    api: string;
    score: number;
  }>;
  unpacking_sequences: string[][];
}

export interface MalwareStats {
  total_analyses: number;
  critical_threats: number;
  high_threats: number;
  medium_threats: number;
  low_threats: number;
  clean_files: number;
  avg_confidence: number;
  avg_malware_score: number;
}

export interface QuickScanResult {
  scan_id: string;
  filename: string;
  scan_type: 'quick';
  timestamp: string;
  file_size: number;
  hashes: {
    md5?: string;
    sha1?: string;
    sha256?: string;
  };
  file_type: string;
  iocs: {
    urls: string[];
    ip_addresses: string[];
    domains: string[];
    email_addresses: string[];
  };
  threat_score: number;
  is_suspicious: boolean;
}

// Service pour les analyses de malware
export const malwareService = {
  // Obtenir le statut du service
  async getServiceStatus() {
    const response = await api.get('/api/malware/status');
    return response.data;
  },

  // Analyser un fichier (analyse standard)
  async analyzeFile(file: File): Promise<{ data: MalwareAnalysis }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/malware/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Analyser un fichier (analyse complète)
  async analyzeFileComprehensive(file: File): Promise<{ data: { analysis: MalwareAnalysis } }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/malware/analyze/comprehensive', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Analyser un fichier (analyse agressive avec tous les outils)
  async analyzeFileAggressive(file: File): Promise<{ data: { analysis: MalwareAnalysis } }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/malware/analyze/aggressive', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Analyser un fichier pour le packing
  async analyzeFilePacking(file: File): Promise<{ data: { packing_analysis: PackingAnalysis } }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/malware/analyze/packing', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Scan rapide d'un fichier
  async quickScan(file: File): Promise<{ data: QuickScanResult }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/malware/scan/quick', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Analyser un répertoire
  async analyzeDirectory(directoryPath: string, recursive: boolean = true) {
    const response = await api.post('/api/malware/analyze/directory', {
      directory_path: directoryPath,
      recursive,
    });
    return response.data;
  },

  // Analyser une URL
  async analyzeUrl(url: string) {
    const response = await api.post('/api/malware/analyze/url', {
      url,
    });
    return response.data;
  },

  // Obtenir l'historique des analyses
  async getAnalysisHistory(limit: number = 10): Promise<{ data: { analyses: MalwareAnalysis[]; count: number; limit: number } }> {
    const response = await api.get(`/api/malware/history?limit=${limit}`);
    return response.data;
  },

  // Obtenir une analyse par ID
  async getAnalysisById(analysisId: string): Promise<{ data: MalwareAnalysis }> {
    const response = await api.get(`/api/malware/analysis/${analysisId}`);
    return response.data;
  },

  // Obtenir les statistiques
  async getStatistics(): Promise<{ data: { statistics: MalwareStats } }> {
    const response = await api.get('/api/malware/stats');
    return response.data;
  },
};

// Fonctions utilitaires
export const malwareUtils = {
  // Obtenir la couleur selon le niveau de menace
  getThreatColor(threatLevel: string): string {
    switch (threatLevel) {
      case 'CRITICAL':
        return 'text-red-600';
      case 'HIGH':
        return 'text-red-500';
      case 'MEDIUM':
        return 'text-orange-500';
      case 'LOW':
        return 'text-yellow-500';
      case 'CLEAN':
        return 'text-green-500';
      case 'WHITELISTED':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  },

  // Obtenir la couleur de fond selon le niveau de menace
  getThreatBgColor(threatLevel: string): string {
    switch (threatLevel) {
      case 'CRITICAL':
        return 'bg-red-600/20 border-red-500';
      case 'HIGH':
        return 'bg-red-500/20 border-red-400';
      case 'MEDIUM':
        return 'bg-orange-500/20 border-orange-400';
      case 'LOW':
        return 'bg-yellow-500/20 border-yellow-400';
      case 'CLEAN':
        return 'bg-green-500/20 border-green-400';
      case 'WHITELISTED':
        return 'bg-blue-500/20 border-blue-400';
      default:
        return 'bg-gray-500/20 border-gray-400';
    }
  },

  // Formater la taille de fichier
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Formater la date
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  },

  // Obtenir l'icône selon le niveau de menace
  getThreatIcon(threatLevel: string): string {
    switch (threatLevel) {
      case 'CRITICAL':
        return '🚨';
      case 'HIGH':
        return '⚠️';
      case 'MEDIUM':
        return '⚡';
      case 'LOW':
        return '⚪';
      case 'CLEAN':
        return '✅';
      case 'WHITELISTED':
        return '🛡️';
      default:
        return '❓';
    }
  },
};
