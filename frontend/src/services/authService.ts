
import { api } from './api';
import {
  LoginPayload,
  AuthResponse,
  RegisterPayload,
} from '../types/auth';

export const authService = {
  login: (payload: LoginPayload) =>
    api.post<AuthResponse>('/auth/login', payload),

  register: (payload: RegisterPayload) =>
    api.post('/auth/register', payload),

  forgotPassword: (email: string) =>
    api.post('/auth/forgot-password', { email }),

  resetPassword: (token: string, password: string) =>
    api.post('/auth/reset-password', { token, password }),

  confirmEmail: (token: string) =>
    api.post('/auth/confirm-email', { token }),
};
