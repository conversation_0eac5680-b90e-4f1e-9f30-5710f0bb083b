import { getToken } from '../utils/auth';

const API_BASE_URL = 'http://localhost:5000/api/incident';

// Types for incident management
export interface InvestigationTicket {
  id: string;
  ticket_number: string;
  short_description: string;
  description: string;
  user_id: string;
  category: string;
  subcategory?: string;
  impact: 'High' | 'Medium' | 'Low';
  urgency: 'High' | 'Medium' | 'Low';
  priority: number;
  assignment_group?: string;
  assigned_to?: string;
  configuration_item?: string;
  contact_type: string;
  location?: string;
  status: 'New' | 'In Progress' | 'On Hold' | 'Resolved' | 'Closed' | 'converted_to_incident';
  created_at: string;
  updated_at: string;
  converted_incident_id?: string;
  timeline?: TimelineEntry[];
  attachments?: Attachment[];
  // Backward compatibility
  title?: string;
  severity?: 'critical' | 'high' | 'medium' | 'low';
}

export interface TimelineEntry {
  timestamp: string;
  action: string;
  user_id: string;
  details?: any;
}

export interface Attachment {
  id: string;
  original_name: string;
  stored_name: string;
  file_path: string;
  file_size: number;
  upload_date: string;
  file_type: string;
}

export interface Incident {
  id: string;
  incident_id: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  closed_at?: string;
  evidence: any[];
  timeline: any[];
  recommendations: string[];
  tags: string[];
  false_positive: boolean;
  escalated: boolean;
  assigned_to?: string;
}

interface NotificationSettings {
  user_id: string;
  email_enabled: boolean;
  telegram_enabled: boolean;
  email_address?: string;
  telegram_chat_id?: string;
}

export interface CreateTicketRequest {
  short_description: string;
  description: string;
  user_id: string;
  category: string;
  subcategory?: string;
  impact: 'High' | 'Medium' | 'Low';
  urgency: 'High' | 'Medium' | 'Low';
  assignment_group?: string;
  assigned_to?: string;
  configuration_item?: string;
  contact_type: string;
  location?: string;
  // Backward compatibility
  title?: string;
  severity?: 'critical' | 'high' | 'medium' | 'low';
}

export interface UpdateTicketRequest {
  title?: string;
  description?: string;
  severity?: 'critical' | 'high' | 'medium' | 'low';
  status?: 'open' | 'in_progress' | 'converted_to_incident' | 'closed';
}

export interface AssignTicketRequest {
  assigned_to: string;
}

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
};

// Ticket management functions
export const createTicket = async (ticketData: CreateTicketRequest): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(ticketData)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create ticket');
  }

  return response.json();
};

export const getTickets = async (): Promise<InvestigationTicket[]> => {
  const response = await fetch(`${API_BASE_URL}/tickets`, {
    method: 'GET',
    headers: getAuthHeaders()
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch tickets');
  }

  return response.json();
};

export const getTicket = async (ticketNumber: string): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}`, {
    method: 'GET',
    headers: getAuthHeaders()
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch ticket');
  }

  return response.json();
};

export const updateTicket = async (ticketNumber: string, updateData: UpdateTicketRequest): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(updateData)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update ticket');
  }

  return response.json();
};

export const assignTicket = async (ticketNumber: string, assignData: AssignTicketRequest): Promise<InvestigationTicket> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}/assign`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(assignData)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to assign ticket');
  }

  return response.json();
};

export const convertTicketToIncident = async (ticketNumber: string): Promise<Incident> => {
  const response = await fetch(`${API_BASE_URL}/tickets/${ticketNumber}/convert`, {
    method: 'POST',
    headers: getAuthHeaders()
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to convert ticket to incident');
  }

  return response.json();
};

// Incident management functions
export const getIncidents = async (): Promise<Incident[]> => {
  const response = await fetch(`${API_BASE_URL}/incidents`, {
    method: 'GET',
    headers: getAuthHeaders()
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch incidents');
  }

  return response.json();
};

export const getIncident = async (incidentId: string): Promise<Incident> => {
  const response = await fetch(`${API_BASE_URL}/incidents/${incidentId}`, {
    method: 'GET',
    headers: getAuthHeaders()
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch incident');
  }

  return response.json();
};

export const updateIncident = async (incidentId: string, updateData: any): Promise<Incident> => {
  const response = await fetch(`${API_BASE_URL}/incidents/${incidentId}`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(updateData)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update incident');
  }

  return response.json();
};

// Notification settings functions
export const getNotificationSettings = async (userId: string): Promise<NotificationSettings | null> => {
  const response = await fetch(`${API_BASE_URL}/notification-settings?user_id=${userId}`, {
    method: 'GET',
    headers: getAuthHeaders()
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to fetch notification settings');
  }

  const data = await response.json();
  return Object.keys(data).length > 0 ? data : null;
};

export const updateNotificationSettings = async (userId: string, settings: Partial<NotificationSettings>): Promise<NotificationSettings> => {
  const response = await fetch(`${API_BASE_URL}/notification-settings`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({ user_id: userId, ...settings })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update notification settings');
  }

  return response.json();
};

// Utility functions
export const getSeverityColor = (severity: string): string => {
  switch (severity?.toLowerCase()) {
    case 'critical':
    case 'high':
      return 'text-red-500 bg-red-100';
    case 'medium':
      return 'text-yellow-500 bg-yellow-100';
    case 'low':
      return 'text-green-500 bg-green-100';
    default:
      return 'text-gray-500 bg-gray-100';
  }
};

export const getPriorityColor = (priority: number): string => {
  switch (priority) {
    case 1:
      return 'text-red-500 bg-red-100';
    case 2:
      return 'text-orange-500 bg-orange-100';
    case 3:
      return 'text-yellow-500 bg-yellow-100';
    case 4:
      return 'text-blue-500 bg-blue-100';
    case 5:
      return 'text-green-500 bg-green-100';
    default:
      return 'text-gray-500 bg-gray-100';
  }
};

export const getPriorityLabel = (priority: number): string => {
  switch (priority) {
    case 1: return 'P1 - Critical';
    case 2: return 'P2 - High';
    case 3: return 'P3 - Medium';
    case 4: return 'P4 - Low';
    case 5: return 'P5 - Planning';
    default: return `P${priority}`;
  }
};

export const getStatusColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'new':
      return 'text-blue-500 bg-blue-100';
    case 'open':
      return 'text-blue-500 bg-blue-100';
    case 'in progress':
    case 'in_progress':
      return 'text-yellow-500 bg-yellow-100';
    case 'on hold':
    case 'on_hold':
      return 'text-orange-500 bg-orange-100';
    case 'resolved':
      return 'text-green-500 bg-green-100';
    case 'closed':
      return 'text-gray-500 bg-gray-100';
    case 'converted_to_incident':
      return 'text-purple-500 bg-purple-100';
    default:
      return 'text-gray-500 bg-gray-100';
  }
};

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString();
};
