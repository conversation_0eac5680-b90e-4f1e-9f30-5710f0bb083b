import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import type { RootState, AppDispatch } from './index';

// Hooks typés pour Redux
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Hooks personnalisés pour les alertes
export const useAlerts = () => {
  const dispatch = useAppDispatch();
  const alerts = useAppSelector((state) => state.alerts.alerts);
  const visibleAlerts = useAppSelector((state) => state.alerts.alerts.filter(alert => alert.isVisible));
  
  return {
    alerts,
    visibleAlerts,
    dispatch,
  };
};

// Hooks personnalisés pour l'utilisateur Redux
export const useReduxAuth = () => {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector((state) => state.user.currentUser);
  const isAuthenticated = useAppSelector((state) => state.user.auth.isAuthenticated);
  const token = useAppSelector((state) => state.user.auth.token);
  const isLoading = useAppSelector((state) => state.user.isLoading);
  const error = useAppSelector((state) => state.user.error);

  return {
    currentUser,
    isAuthenticated,
    token,
    isLoading,
    error,
    dispatch,
  };
};

// Hook pour les préférences utilisateur
export const useUserPreferences = () => {
  const preferences = useAppSelector((state) => state.user.currentUser?.preferences);
  const dispatch = useAppDispatch();
  
  return {
    preferences,
    dispatch,
  };
};

// Hook pour le rôle utilisateur
export const useUserRole = () => {
  const role = useAppSelector((state) => state.user.currentUser?.role);
  const isAdmin = role === 'admin';
  const isModerator = role === 'moderator' || isAdmin;
  const isUser = role === 'user';
  
  return {
    role,
    isAdmin,
    isModerator,
    isUser,
  };
};
