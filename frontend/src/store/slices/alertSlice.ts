import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Alert {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  title?: string;
  duration?: number; // en millisecondes, 0 = permanent
  timestamp: number;
  isVisible: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

interface AlertState {
  alerts: Alert[];
  maxAlerts: number;
  defaultDuration: number;
  defaultPosition: Alert['position'];
}

const initialState: AlertState = {
  alerts: [],
  maxAlerts: 5, // Maximum d'alertes affichées simultanément
  defaultDuration: 5000, // 5 secondes par défaut
  defaultPosition: 'top-right',
};

const alertSlice = createSlice({
  name: 'alerts',
  initialState,
  reducers: {
    // Ajouter une nouvelle alerte
    addAlert: (state, action: PayloadAction<Omit<Alert, 'id' | 'timestamp' | 'isVisible'>>) => {
      const newAlert: Alert = {
        ...action.payload,
        id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        isVisible: true,
        duration: action.payload.duration ?? state.defaultDuration,
        position: action.payload.position ?? state.defaultPosition,
      };

      // Ajouter la nouvelle alerte au début
      state.alerts.unshift(newAlert);

      // Limiter le nombre d'alertes affichées
      if (state.alerts.length > state.maxAlerts) {
        state.alerts = state.alerts.slice(0, state.maxAlerts);
      }
    },

    // Supprimer une alerte par ID
    removeAlert: (state, action: PayloadAction<string>) => {
      state.alerts = state.alerts.filter(alert => alert.id !== action.payload);
    },

    // Masquer une alerte (animation de sortie)
    hideAlert: (state, action: PayloadAction<string>) => {
      const alert = state.alerts.find(alert => alert.id === action.payload);
      if (alert) {
        alert.isVisible = false;
      }
    },

    // Supprimer toutes les alertes
    clearAllAlerts: (state) => {
      state.alerts = [];
    },

    // Supprimer les alertes par type
    clearAlertsByType: (state, action: PayloadAction<Alert['type']>) => {
      state.alerts = state.alerts.filter(alert => alert.type !== action.payload);
    },

    // Mettre à jour les paramètres des alertes
    updateAlertSettings: (state, action: PayloadAction<Partial<Pick<AlertState, 'maxAlerts' | 'defaultDuration' | 'defaultPosition'>>>) => {
      if (action.payload.maxAlerts !== undefined) {
        state.maxAlerts = action.payload.maxAlerts;
        // Ajuster le nombre d'alertes si nécessaire
        if (state.alerts.length > state.maxAlerts) {
          state.alerts = state.alerts.slice(0, state.maxAlerts);
        }
      }
      if (action.payload.defaultDuration !== undefined) {
        state.defaultDuration = action.payload.defaultDuration;
      }
      if (action.payload.defaultPosition !== undefined) {
        state.defaultPosition = action.payload.defaultPosition;
      }
    },

    // Marquer une alerte comme lue (pour les notifications persistantes)
    markAlertAsRead: (state, action: PayloadAction<string>) => {
      const alert = state.alerts.find(alert => alert.id === action.payload);
      if (alert) {
        // Ajouter une propriété read si nécessaire
        (alert as any).read = true;
      }
    },
  },
});

export const {
  addAlert,
  removeAlert,
  hideAlert,
  clearAllAlerts,
  clearAlertsByType,
  updateAlertSettings,
  markAlertAsRead,
} = alertSlice.actions;

export default alertSlice.reducer;

// Sélecteurs
export const selectAllAlerts = (state: { alerts: AlertState }) => state.alerts.alerts;
export const selectVisibleAlerts = (state: { alerts: AlertState }) => 
  state.alerts.alerts.filter(alert => alert.isVisible);
export const selectAlertsByType = (state: { alerts: AlertState }, type: Alert['type']) =>
  state.alerts.alerts.filter(alert => alert.type === type);
export const selectAlertSettings = (state: { alerts: AlertState }) => ({
  maxAlerts: state.alerts.maxAlerts,
  defaultDuration: state.alerts.defaultDuration,
  defaultPosition: state.alerts.defaultPosition,
});
