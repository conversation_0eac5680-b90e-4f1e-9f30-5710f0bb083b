export type LoginPayload = {
  email: string;
  password: string;
  remember_me?: boolean;
};

export type RegisterPayload = {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  username: string;
  date_of_birth: string;
  gender: string;
};

export type AuthResponse = {
  token: string;
  refresh_token?: string;
  user: {
    id: string;
    email: string;
    username: string;
    role: string;
  };
};
