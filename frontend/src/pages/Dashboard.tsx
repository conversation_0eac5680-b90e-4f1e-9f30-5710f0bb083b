import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import { Shield, Activity, AlertTriangle, CheckCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export default function Dashboard() {
  const { user } = useAuth();
  const { role } = useRole();
  const navigate = useNavigate();

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
              <Shield className="w-12 h-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-white mb-4">
            🎉 Welcome to PICA Dashboard!
          </h1>
          <p className="text-xl text-purple-200 mb-2">
            Congratulations! You are now connected to the PICA automated cybersecurity platform.
          </p>
          <div className="inline-flex items-center bg-green-600/20 text-green-300 px-4 py-2 rounded-xl border border-green-500/30">
            <CheckCircle className="w-5 h-5 mr-2" />
            ✅ Login successful
          </div>
        </div>
      </div>

      {/* User Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center">
              <Activity className="w-6 h-6 text-blue-400" />
            </div>
            <div>
              <h3 className="text-white font-semibold">User</h3>
              <p className="text-gray-300">{user?.username || user?.email}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center">
              <Shield className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h3 className="text-white font-semibold">Role</h3>
              <p className="text-gray-300 capitalize">{role}</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 shadow-xl">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-400" />
            </div>
            <div>
              <h3 className="text-white font-semibold">Status</h3>
              <p className="text-green-300">Connected</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <h2 className="text-2xl font-bold text-white mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => navigate('/redux-demo')}
            className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105"
          >
            🔍 Test Redux Slices
          </button>
          <button
            onClick={() => navigate('/auth/login')}
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105"
          >
            🔄 Back to login
          </button>
          <button
            onClick={() => navigate('/settings')}
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105"
          >
            ⚙️ Settings
          </button>
          <button
            onClick={() => navigate('/analytics')}
            className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white p-4 rounded-xl transition-all duration-200 transform hover:scale-105"
          >
            📊 Analytics
          </button>
        </div>
      </div>
    </div>
  );
}
