import { useState, useEffect } from 'react';
import {
  Target,
  Shield,
  Search,
  Play,
  Pause,
  Eye,
  Clock,
  CheckCircle,
  AlertTriangle,
  Network,
  Bug,
  Globe,
  Zap,
  X,
  RefreshCw,
  Activity,
  Settings,
  StopCircle,
  Trash2,
  Download,
  FileDown
} from 'lucide-react';
import Cookies from 'js-cookie';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import { useModalContext } from '../contexts/ModalContext';
import { getRunningScans, getScanDetailsWithTools, stopScan, cancelScan, deleteScan, restartScan, getLiveScanLogs, startRobustWebScan, getRobustScanStatus, stopRobustScan, getActiveRobustScans, getRobustScanLogs } from '../services/api';

interface ScanResult {
  id: string;
  scan_id: string;
  category: 'network' | 'web' | 'vulnerability' | 'deep';
  scan_type: string;
  target: string;
  status: 'running' | 'completed' | 'failed';
  start_time: string;
  end_time?: string;
  duration?: number;
  vulnerabilities?: any[];
  ports?: any[];
  summary?: any;
  isRobust?: boolean; // Flag to indicate if this is a robust scan
  progress?: number;
  currentTool?: string;
  tools?: string[];
  results?: any;
  user_id?: string; // Username of the user who launched the scan
}

export default function Pentesting() {
  const { user } = useAuth();
  const { isAdmin } = useRole();
  const { showAlert, showConfirm } = useModalContext();
  const [target, setTarget] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedScanType, setSelectedScanType] = useState('');
  const [ports, setPorts] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(true);

  const [selectedScanDetails, setSelectedScanDetails] = useState<any>(null);
  const [showScanDetails, setShowScanDetails] = useState(false);
  const [loadingScanDetails, setLoadingScanDetails] = useState(false);
  const [exportingPDF, setExportingPDF] = useState(false);
  const [exportingCSV, setExportingCSV] = useState(false);

  // Filter states for scan history
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterScanType, setFilterScanType] = useState<string>('all');
  const [filterUser, setFilterUser] = useState<string>('all');

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scansPerPage] = useState<number>(10);

  // States for running scans monitoring
  const [runningScans, setRunningScans] = useState<any[]>([]);
  const [showRunningScans, setShowRunningScans] = useState(false);
  const [selectedRunningScan, setSelectedRunningScan] = useState<string | null>(null);
  const [runningScansInterval, setRunningScansInterval] = useState<NodeJS.Timeout | null>(null);

  // States for logs
  const [scanLogs, setScanLogs] = useState<any[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const [logsInterval, setLogsInterval] = useState<NodeJS.Timeout | null>(null);

  // Load scan history on component mount
  useEffect(() => {
    console.log('🚀 useEffect called - loading scan history');
    loadScanHistory();
    loadRunningScans();
  }, []);

  // Clean up polling on component unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Component unmounting, cleaning up polling');
      stopRunningScansPolling();
      stopLogsPolling();
    };
  }, [runningScansInterval, logsInterval]);

  // Monitor robust scans and update their status
  useEffect(() => {
    const monitorRobustScans = async () => {
      try {
        // Get all robust scans from scan results
        const robustScans = scanResults.filter(scan => scan.isRobust && scan.status === 'running');

        for (const scan of robustScans) {
          try {
            const status = await getRobustScanStatus(scan.scan_id);

            // Update scan if status changed
            if (status.status !== scan.status || status.progress !== scan.progress) {
              setScanResults(prev => prev.map(s =>
                s.scan_id === scan.scan_id
                  ? {
                      ...s,
                      status: status.status,
                      progress: status.progress || s.progress,
                      currentTool: status.current_tool || s.currentTool
                    }
                  : s
              ));
            }
          } catch (error) {
            console.error(`Error monitoring robust scan ${scan.scan_id}:`, error);
          }
        }
      } catch (error) {
        console.error('Error monitoring robust scans:', error);
      }
    };

    // Monitor robust scans every 3 seconds
    const robustScanInterval = setInterval(monitorRobustScans, 3000);

    return () => {
      clearInterval(robustScanInterval);
    };
  }, [scanResults]);

  const loadScanHistory = async () => {
    console.log('🚀 loadScanHistory function called');
    try {
      setLoadingHistory(true);
      console.log('🔍 Loading scan history...');

      const token = Cookies.get('token');
      console.log('🔑 Token exists:', !!token);
      console.log('🔑 Token value:', token ? token.substring(0, 50) + '...' : 'null');

      if (!token) {
        console.error('❌ No authentication token found in cookies');
        setScanResults([]);
        return;
      }

      // Vérifier si le token est expiré
      try {
        const tokenPayload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        console.log('🔑 Token expires at:', new Date(tokenPayload.exp * 1000));
        console.log('🔑 Current time:', new Date(currentTime * 1000));

        if (tokenPayload.exp < currentTime) {
          console.error('❌ Token has expired');
          Cookies.remove('token');
          setScanResults([]);
          return;
        }
      } catch (e) {
        console.error('❌ Invalid token format:', e);
        Cookies.remove('token');
        setScanResults([]);
        return;
      }

      const response = await fetch('http://localhost:5000/scan/pentesting/scans', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 History response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ History data received:', data);

        // Vérifier si des scans existent
        if (data.scans && data.scans.length > 0) {
          const formattedScans = data.scans.map((scan: any) => ({
            id: scan.scan_id || scan.mongo_id,
            scan_id: scan.scan_id,
            category: scan.category || 'network', // Utiliser directement le champ category du backend
            scan_type: scan.scan_type || 'unknown',
            target: scan.target,
            status: scan.status,
            start_time: scan.start_time,
            end_time: scan.end_time,
            duration: scan.duration,
            vulnerabilities: scan.vulnerabilities || [],
            ports: scan.ports || [],
            summary: scan.summary || {},
            user_id: scan.user_id // Include username for admin view
          }));
          // Sort scans by start date (most recent first)
          const sortedScans = formattedScans.sort((a, b) => {
            const dateA = new Date(a.start_time || 0);
            const dateB = new Date(b.start_time || 0);
            return dateB.getTime() - dateA.getTime(); // Most recent first
          });

          setScanResults(sortedScans);
          console.log('✅ Formatted and sorted scans:', sortedScans);
          console.log('📊 Categories found:', sortedScans.map(s => s.category));
        } else {
          // Empty database - no scans
          setScanResults([]);
          console.log('📭 No scans found in database');
        }
      } else {
        const errorText = await response.text();
        console.error('❌ History failed:', response.status, response.statusText, errorText);
        console.error('❌ Response headers:', Object.fromEntries(response.headers.entries()));

        // Si erreur d'authentification, laisser l'intercepteur axios gérer la déconnexion
        if (response.status === 422 || response.status === 401) {
          console.error('❌ Authentication failed - will be handled by interceptor');
          return;
        }

        setScanResults([]); // In case of error, display empty list
      }
    } catch (error) {
      console.error('❌ History network error:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      // Don't display demo data - leave empty if error
      setScanResults([]);
    } finally {
      setLoadingHistory(false);
    }
  };

  // Fonction pour récupérer les détails d'un scan
  const loadScanDetails = async (scanId: string) => {
    try {
      setLoadingScanDetails(true);
      console.log('🔍 Loading scan details for:', scanId);

      // Check if this is a robust scan
      const scan = scanResults.find(s => s.scan_id === scanId);
      const isRobustScan = scan?.isRobust;

      if (isRobustScan) {
        console.log('🚀 Loading robust scan details and logs');

        // Get robust scan status and logs
        const [statusData, logsData] = await Promise.all([
          getRobustScanStatus(scanId),
          getRobustScanLogs(scanId, 100)
        ]);

        const combinedData = {
          ...statusData,
          logs: logsData.logs,
          scan_type: scan.scan_type,
          category: scan.category,
          target: scan.target,
          tools: scan.tools || [],
          isRobust: true
        };

        console.log('✅ Robust scan details received:', combinedData);
        setSelectedScanDetails(combinedData);
        setShowScanDetails(true);
      } else {
        // Use regular scan details API
        const token = Cookies.get('token');
        if (!token) {
          console.error('❌ No authentication token found');
          return;
        }

        const response = await fetch(`http://localhost:5000/scan/pentesting/scan/${scanId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('📡 Scan details response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Scan details received:', data);
          setSelectedScanDetails(data);
          setShowScanDetails(true);
        } else {
          const errorText = await response.text();
          console.error('❌ Failed to load scan details:', response.status, errorText);
          showAlert({
            type: 'error',
            title: 'Erreur de chargement',
            message: `Erreur lors du chargement des détails: ${response.status}`
          });
        }
      }
    } catch (error) {
      console.error('❌ Error loading scan details:', error);
      showAlert({
        type: 'error',
        title: 'Erreur de connexion',
        message: `Erreur de connexion: ${error}`
      });
    } finally {
      setLoadingScanDetails(false);
    }
  };

  // Functions to manage running scans
  const loadRunningScans = async () => {
    try {
      console.log('🏃 Loading running scans...');
      const data = await getRunningScans();
      setRunningScans(data.running_scans || []);
      console.log('✅ Running scans loaded:', data.running_scans?.length || 0);
    } catch (error) {
      console.error('❌ Error loading running scans:', error);
      setRunningScans([]);
    }
  };

  const startRunningScansPolling = () => {
    stopRunningScansPolling();

    const interval = setInterval(() => {
      loadRunningScans();
    }, 5000); // Rafraîchir toutes les 5 secondes

    setRunningScansInterval(interval);
    console.log('🔄 Running scans polling started');
  };

  const stopRunningScansPolling = () => {
    if (runningScansInterval) {
      clearInterval(runningScansInterval);
      setRunningScansInterval(null);
      console.log('🛑 Running scans polling stopped');
    }
  };

  const loadRunningScanDetails = async (scanId: string) => {
    try {
      console.log('🔍 Loading running scan details for:', scanId);
      const details = await getScanDetailsWithTools(scanId);
      setSelectedScanDetails(details);
      setSelectedRunningScan(scanId);
      console.log('✅ Running scan details loaded:', details);

      // Also load logs if it's a running scan
      if (details.status === 'running') {
        loadScanLogs(scanId);
      }
    } catch (error) {
      console.error('❌ Error loading running scan details:', error);
    }
  };

  // Functions to manage logs
  const loadScanLogs = async (scanId: string) => {
    try {
      console.log('📋 Loading scan logs for:', scanId);
      const logsData = await getLiveScanLogs(scanId);
      const logs = logsData.latest_logs || [];
      setScanLogs(logs);
      console.log('✅ Scan logs loaded:', logs.length, 'entries');

      // If no logs, check if it's a recent or old scan
      if (logs.length === 0) {
        console.log('⚠️ No logs found for scan:', scanId);
        console.log('   Scan status:', logsData.status);
        console.log('   Current tool:', logsData.current_tool);
      }
    } catch (error) {
      console.error('❌ Error loading scan logs:', error);
      setScanLogs([]);
    }
  };

  const startLogsPolling = (scanId: string) => {
    stopLogsPolling();

    const interval = setInterval(() => {
      loadScanLogs(scanId);
    }, 3000); // Refresh every 3 seconds

    setLogsInterval(interval);
    console.log('🔄 Logs polling started for scan:', scanId);
  };

  const stopLogsPolling = () => {
    if (logsInterval) {
      clearInterval(logsInterval);
      setLogsInterval(null);
      console.log('🛑 Logs polling stopped');
    }
  };

  // Functions to manage stopping and deleting scans
  const handleStopScan = async (scanId: string) => {
    try {
      console.log('🛑 Stopping scan:', scanId);
      await stopScan(scanId);
      console.log('✅ Scan stopped successfully');

      // Rafraîchir les données
      await loadRunningScans();
      await loadScanHistory();

      // Si c'était le scan sélectionné, le désélectionner
      if (selectedRunningScan === scanId) {
        setSelectedRunningScan(null);
        setSelectedScanDetails(null);
      }
    } catch (error) {
      console.error('❌ Error stopping scan:', error);
      showAlert({
        type: 'error',
        title: 'Erreur d\'arrêt',
        message: 'Failed to stop scan: ' + error
      });
    }
  };

  const handleCancelScan = async (scanId: string) => {
    const confirmed = await showConfirm({
      type: 'danger',
      title: 'Annuler le scan',
      message: 'Êtes-vous sûr de vouloir annuler ce scan ? Cette action ne peut pas être annulée.',
      confirmText: 'Annuler le scan',
      cancelText: 'Garder le scan'
    });

    if (!confirmed) {
      return;
    }

    try {
      console.log('❌ Cancelling scan:', scanId);
      await cancelScan(scanId);
      console.log('✅ Scan cancelled successfully');

      // Rafraîchir les données
      await loadRunningScans();
      await loadScanHistory();

      // Si c'était le scan sélectionné, le désélectionner
      if (selectedRunningScan === scanId) {
        setSelectedRunningScan(null);
        setSelectedScanDetails(null);
      }
    } catch (error) {
      console.error('❌ Error cancelling scan:', error);
      showAlert({
        type: 'error',
        title: 'Erreur d\'annulation',
        message: 'Failed to cancel scan: ' + error
      });
    }
  };

  const handleDeleteScan = async (scanId: string) => {
    const confirmed = await showConfirm({
      type: 'danger',
      title: 'Delete scan',
      message: 'Are you sure you want to delete this scan? This will permanently delete all scan data.',
      confirmText: 'Delete',
      cancelText: 'Cancel'
    });

    if (!confirmed) {
      return;
    }

    try {
      console.log('🗑️ Deleting scan:', scanId);
      await deleteScan(scanId);
      console.log('✅ Scan deleted successfully');

      // Rafraîchir les données
      await loadScanHistory();

      // Si c'était le scan sélectionné dans les détails, le désélectionner
      if (showScanDetails && selectedScanDetails?.scan_id === scanId) {
        setShowScanDetails(false);
        setSelectedScanDetails(null);
      }
    } catch (error) {
      console.error('❌ Error deleting scan:', error);
      showAlert({
        type: 'error',
        title: 'Erreur de suppression',
        message: 'Failed to delete scan: ' + error
      });
    }
  };

  // Fonction pour exporter le rapport PDF
  const handleExportPDF = async (scanId: string) => {
    try {
      setExportingPDF(true);
      console.log('📄 Exporting PDF for scan:', scanId);

      const token = Cookies.get('token');
      if (!token) {
        showAlert({
          type: 'error',
          title: 'Authentication Error',
          message: 'Please log in to export reports'
        });
        return;
      }

      // Vérifier que le scan est terminé
      if (selectedScanDetails?.status === 'running') {
        showAlert({
          type: 'warning',
          title: 'Scan en cours',
          message: 'Veuillez attendre que le scan soit terminé avant d\'exporter le rapport.'
        });
        return;
      }

      const response = await fetch(`http://localhost:5000/api/export/scan/${scanId}/pdf`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      });

      if (response.ok) {
        // Créer un blob à partir de la réponse
        const blob = await response.blob();

        // Créer un URL temporaire pour le blob
        const url = window.URL.createObjectURL(blob);

        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = url;

        // Générer le nom du fichier
        const target = selectedScanDetails?.target?.replace(/[/:]/g, '_') || 'unknown';
        const category = selectedScanDetails?.category || 'scan';
        const scanType = selectedScanDetails?.scan_type || 'basic';
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');

        link.download = `PICA_Report_${category}_${scanType}_${target}_${timestamp}.pdf`;

        // Déclencher le téléchargement
        document.body.appendChild(link);
        link.click();

        // Nettoyer
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        showAlert({
          type: 'success',
          title: 'Export successful',
          message: 'PDF report downloaded successfully.'
        });

        console.log('✅ PDF exported successfully');
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to export PDF:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Erreur d\'export',
          message: errorData.message || 'Erreur lors de l\'export du rapport PDF'
        });
      }
    } catch (error) {
      console.error('❌ Error exporting PDF:', error);
      showAlert({
        type: 'error',
        title: 'Export error',
        message: 'An error occurred during PDF report export'
      });
    } finally {
      setExportingPDF(false);
    }
  };

  // Fonction pour exporter tous les scans en CSV
  const handleExportAllCSV = async () => {
    try {
      setExportingCSV(true);
      console.log('📊 Exporting all scans to CSV');

      const token = Cookies.get('token');
      if (!token) {
        showAlert({
          type: 'error',
          title: 'Authentication Error',
          message: 'Please log in to export data'
        });
        return;
      }

      const response = await fetch(`http://localhost:5000/api/export/scans/csv`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      });

      if (response.ok) {
        // Créer un blob à partir de la réponse
        const blob = await response.blob();

        // Créer un URL temporaire pour le blob
        const url = window.URL.createObjectURL(blob);

        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = url;

        // Générer le nom du fichier
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        link.download = `PICA_All_Scans_Export_${timestamp}.csv`;

        // Déclencher le téléchargement
        document.body.appendChild(link);
        link.click();

        // Nettoyer
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        showAlert({
          type: 'success',
          title: 'Export successful',
          message: `${scanResults.length} scan(s) exported successfully to CSV.`
        });

        console.log('✅ CSV exported successfully');
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to export CSV:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Erreur d\'export',
          message: errorData.message || 'Erreur lors de l\'export CSV'
        });
      }
    } catch (error) {
      console.error('❌ Error exporting CSV:', error);
      showAlert({
        type: 'error',
        title: 'Export error',
        message: 'An error occurred during CSV export'
      });
    } finally {
      setExportingCSV(false);
    }
  };

  const handleRestartScan = async (scanId: string) => {
    const confirmed = await showConfirm({
      type: 'info',
      title: 'Reprendre le scan',
      message: 'Êtes-vous sûr de vouloir reprendre ce scan arrêté ? Cela créera un nouveau scan avec les mêmes paramètres.',
      confirmText: 'Reprendre',
      cancelText: 'Annuler'
    });

    if (!confirmed) {
      return;
    }

    try {
      console.log('🔄 Resuming scan:', scanId);
      const result = await restartScan(scanId);
      console.log('✅ Scan resumed successfully:', result);

      // Rafraîchir les données - wait a bit for the scan to be properly created
      setTimeout(async () => {
        await loadRunningScans();
        await loadScanHistory();
      }, 1000);

      showAlert({
        type: 'success',
        title: 'Scan resumed',
        message: 'Scan has been resumed successfully'
      });
    } catch (error) {
      console.error('❌ Error resuming scan:', error);
      showAlert({
        type: 'error',
        title: 'Erreur de reprise',
        message: 'Failed to resume scan: ' + error
      });
    }
  };



  // Fonctions utilitaires pour l'affichage
  const getToolIcon = (tool: string) => {
    switch (tool) {
      case 'nmap': return '🔍';
      case 'nikto': return '🌐';
      case 'sqlmap': return '💉';
      case 'dirb': return '📁';
      case 'gobuster': return '🔎';
      case 'openvas': return '🛡️';
      case 'metasploit': return '⚡';
      case 'zap': return '🕷️';
      // Deep scan phases
      case 'Phase 1: Network Scanning': return '🔍';
      case 'Phase 1: NMAP': return '🔍';
      case 'Phase 1: NMAP Complete': return '✅';
      case 'Phase 1: OPENVAS': return '🛡️';
      case 'Phase 1: OPENVAS Complete': return '✅';
      case 'Phase 1: METASPLOIT': return '⚡';
      case 'Phase 1: METASPLOIT Complete': return '✅';
      case 'Phase 2: Web Scanning': return '🌐';
      case 'Phase 2: NIKTO': return '🌐';
      case 'Phase 2: NIKTO Complete': return '✅';
      case 'Phase 2: SQLMAP': return '💉';
      case 'Phase 2: SQLMAP Complete': return '✅';
      case 'Phase 2: DIRB': return '📁';
      case 'Phase 2: DIRB Complete': return '✅';
      case 'Phase 2: GOBUSTER': return '🔎';
      case 'Phase 2: GOBUSTER Complete': return '✅';
      case 'Phase 2: ZAP': return '🕷️';
      case 'Phase 2: ZAP Complete': return '✅';
      case 'Phase 3: Analysis': return '📊';
      case 'Phase 3: Consolidating Ports': return '🔗';
      case 'Phase 3: Consolidating Vulnerabilities': return '🔗';
      case 'Phase 3: Calculating Summary': return '📊';
      case 'Phase 3: Finalizing': return '✅';
      case 'Phase 3: Saving Results': return '💾';
      case 'Completing Deep Scan': return '🏁';
      default: return '🔧';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-blue-400';
      case 'completed': return 'text-green-400';
      case 'failed': return 'text-red-400';
      case 'error': return 'text-orange-400';
      case 'stopped': return 'text-yellow-400';
      case 'unavailable': return 'text-gray-400';
      case 'pending': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'network': return '🟦';
      case 'web': return '🟩';
      case 'vulnerability': return '🟪';
      case 'deep': return '🟥';
      default: return '🔍';
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'info': return 'text-blue-400';
      case 'debug': return 'text-gray-400';
      case 'error': return 'text-red-400';
      case 'warning': return 'text-yellow-400';
      default: return 'text-white';
    }
  };

  const getLogIcon = (eventType: string) => {
    switch (eventType) {
      case 'scan_start': return '🚀';
      case 'tool_start': return '🔧';
      case 'tool_progress': return '📊';
      case 'tool_result': return '✅';
      case 'tool_error': return '❌';
      case 'tool_output': return '📝';
      case 'scan_complete': return '🏁';
      default: return '📋';
    }
  };

  const formatLogTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // Filter scan results based on selected filters
  const getFilteredScanResults = () => {
    return scanResults.filter(scan => {
      // Category filter
      if (filterCategory !== 'all' && scan.category !== filterCategory) {
        return false;
      }

      // Status filter
      if (filterStatus !== 'all' && scan.status !== filterStatus) {
        return false;
      }

      // Scan type filter
      if (filterScanType !== 'all' && scan.scan_type !== filterScanType) {
        return false;
      }

      // User filter (only for admin)
      if (isAdmin && filterUser !== 'all' && scan.user_id !== filterUser) {
        return false;
      }

      return true;
    });
  };

  // Get unique values for filter options
  const getFilterOptions = () => {
    const categories = [...new Set(scanResults.map(scan => scan.category))];
    const statuses = [...new Set(scanResults.map(scan => scan.status))];
    const scanTypes = [...new Set(scanResults.map(scan => scan.scan_type))];
    const users = isAdmin ? [...new Set(scanResults.map(scan => scan.user_id).filter(Boolean))] : [];

    return { categories, statuses, scanTypes, users };
  };

  // Reset all filters
  const resetFilters = () => {
    setFilterCategory('all');
    setFilterStatus('all');
    setFilterScanType('all');
    setFilterUser('all');
    setCurrentPage(1); // Reset to first page when filters are reset
  };

  // Get paginated scan results
  const getPaginatedScanResults = () => {
    const filteredScans = getFilteredScanResults();
    const startIndex = (currentPage - 1) * scansPerPage;
    const endIndex = startIndex + scansPerPage;
    return filteredScans.slice(startIndex, endIndex);
  };

  // Calculate pagination info
  const getPaginationInfo = () => {
    const filteredScans = getFilteredScanResults();
    const totalScans = filteredScans.length;
    const totalPages = Math.ceil(totalScans / scansPerPage);
    const startIndex = (currentPage - 1) * scansPerPage + 1;
    const endIndex = Math.min(currentPage * scansPerPage, totalScans);

    return {
      totalScans,
      totalPages,
      startIndex,
      endIndex,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1
    };
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of scan history section
    document.getElementById('scan-history-section')?.scrollIntoView({ behavior: 'smooth' });
  };

  const scanCategories = [
    {
      id: 'network',
      name: 'Network Scan',
      description: '🟦 Nmap + OpenVAS + Metasploit (parallel)',
      icon: Network,
      color: 'from-blue-500 to-cyan-500',
      types: [
        { id: 'basic', name: 'Basic Scan', description: 'Common ports + known vulnerabilities' },
        { id: 'aggressive', name: 'Aggressive Scan', description: 'Complete fingerprinting + OS detection + CVE' },
        { id: 'stealth', name: 'Stealth Scan', description: 'Slow and discreet scan (no OS detect)' },
        { id: 'comprehensive', name: 'Comprehensive Scan', description: 'Ports + vulnerabilities + extended scans' }
      ]
    },
    {
      id: 'web',
      name: 'Web Scan',
      description: '🟩 Nikto + SQLMap + Dirb + GoBuster + Zap (parallel)',
      icon: Globe,
      color: 'from-green-500 to-emerald-500',
      types: [
        { id: 'basic', name: 'Basic Web Scan', description: 'Known vulnerabilities check + simple directories' },
        { id: 'aggressive', name: 'Aggressive Web Scan', description: 'Deep testing + advanced injections' },
        { id: 'stealth', name: 'Stealth Web Scan', description: 'Slow, discreet, low-intrusive scan' },
        { id: 'comprehensive', name: 'Comprehensive Web Scan', description: 'All tools + active Zap scan' }
      ]
    },
    {
      id: 'vulnerability',
      name: 'Vulnerability Scan',
      description: '🟪 OpenVAS + Metasploit (parallel)',
      icon: Shield,
      color: 'from-purple-500 to-pink-500',
      types: [
        { id: 'basic', name: 'Basic Vulnerability Scan', description: 'Common vulnerabilities, default config' },
        { id: 'aggressive', name: 'Aggressive Vulnerability Scan', description: 'Critical vulnerabilities + detected exploits' },
        { id: 'stealth', name: 'Stealth Vulnerability Scan', description: 'Minimal vulnerabilities without disruption' },
        { id: 'comprehensive', name: 'Comprehensive Vulnerability Scan', description: 'Everything detected + exploit pre-validation' }
      ]
    },
    {
      id: 'deep',
      name: 'Deep Scan',
      description: '🟥 ALL tools (Network + Web + Vulnerability)',
      icon: Zap,
      color: 'from-orange-500 to-red-500',
      types: [
        { id: 'basic', name: 'Basic Deep Scan', description: 'All tools with low intensity' },
        { id: 'aggressive', name: 'Aggressive Deep Scan', description: 'All tools with high intensity' },
        { id: 'stealth', name: 'Stealth Deep Scan', description: 'All tools in stealth mode' },
        { id: 'comprehensive', name: 'Comprehensive Deep Scan', description: 'Complete audit with all tools' }
      ]
    }
  ];

  const handleStartScan = async () => {
    if (!target || !selectedCategory || !selectedScanType) return;

    setIsScanning(true);

    try {
      let apiUrl = '';
      let requestBody: any = {};

      // Déterminer l'URL et les paramètres selon la catégorie
      switch (selectedCategory) {
        case 'network':
          apiUrl = 'http://localhost:5000/scan/pentesting/scan/network';
          requestBody = {
            target,
            scan_type: selectedScanType,
            ports: ports || undefined // Envoyer les ports si spécifiés
          };
          break;
        case 'web':
          // Use robust web scan API for better reliability
          try {
            const targetUrl = target.startsWith('http') ? target : `http://${target}`;
            const result = await startRobustWebScan(targetUrl, selectedScanType);

            console.log('✅ Robust web scan started successfully:', result);

            // Add the new scan to the list
            const newScan: ScanResult = {
              id: result.scan_id,
              scan_id: result.scan_id,
              category: 'web',
              scan_type: selectedScanType,
              target: targetUrl,
              status: 'running',
              start_time: new Date().toISOString(),
              progress: 0,
              currentTool: 'initializing',
              tools: result.tools || ['nikto', 'sqlmap', 'dirb', 'gobuster', 'zap'],
              results: {},
              isRobust: true // Flag to indicate this is a robust scan
            };

            setScanResults(prev => [newScan, ...prev]);
            setIsScanning(false);

            // Show success message
            console.log(`🟩 ${selectedScanType.charAt(0).toUpperCase() + selectedScanType.slice(1)} web scan started on ${targetUrl}`);
            return; // Exit early since we handled the web scan
          } catch (error) {
            console.error('❌ Error starting robust web scan:', error);
            setIsScanning(false);
            return;
          }
        case 'vulnerability':
          apiUrl = 'http://localhost:5000/scan/pentesting/scan/vulnerability';
          requestBody = {
            target,
            scan_type: selectedScanType
          };
          break;
        case 'deep':
          apiUrl = 'http://localhost:5000/scan/pentesting/scan/deep';
          requestBody = {
            target,
            scan_type: selectedScanType,
            tools: ['nmap', 'nikto', 'openvas', 'metasploit'],
            options: ports ? { ports } : {}
          };
          break;
        default:
          // Fallback vers l'API unifiée
          apiUrl = 'http://localhost:5000/scan/pentesting/scan/start';
          requestBody = {
            target,
            scan_type: selectedScanType,
            ports: ports || undefined
          };
      }

      console.log('🚀 Starting scan with:', {
        category: selectedCategory,
        scanType: selectedScanType,
        target,
        ports,
        apiUrl,
        requestBody
      });

      console.log('📡 Making request to:', apiUrl);
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Cookies.get('token')}`
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Scan started successfully:', result);

        // Ajouter le nouveau scan à la liste
        const newScan: ScanResult = {
          id: result.scan_id || Date.now().toString(),
          scan_id: result.scan_id || Date.now().toString(),
          category: selectedCategory as any,
          scan_type: selectedScanType,
          target,
          status: 'running',
          start_time: new Date().toISOString(),
          vulnerabilities: [],
          ports: [],
          summary: {}
        };
        setScanResults(prev => [newScan, ...prev]);

        // Pour tous les scans, utiliser le moniteur
        // Pas de modal de progression spéciale pour le deep scan

        // PAS de refresh automatique - l'utilisateur utilisera le bouton Refresh manuel
        console.log('✅ Scan started successfully. Use manual refresh button to see updated results.');
      } else {
        const errorData = await response.text();
        console.error('❌ Scan failed:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Erreur de démarrage',
          message: `Erreur lors du démarrage du scan: ${response.status} - ${errorData}`
        });
      }
    } catch (error) {
      console.error('❌ Network error:', error);
      showAlert({
        type: 'error',
        title: 'Erreur de connexion',
        message: `Erreur de connexion: ${error}`
      });
    } finally {
      setIsScanning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Clock className="w-4 h-4 text-yellow-400 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <AlertTriangle className="w-4 h-4 text-red-400" />;
      case 'stopped':
        return <Pause className="w-4 h-4 text-yellow-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };



  const getScanCategoryInfo = (category: string) => {
    const categoryMap: { [key: string]: any } = {
      'network': {
        icon: Network,
        name: 'Network Scan',
        color: 'from-blue-500 to-cyan-500'
      },
      'web': {
        icon: Globe,
        name: 'Web Scan',
        color: 'from-green-500 to-emerald-500'
      },
      'vulnerability': {
        icon: Shield,
        name: 'Vulnerability Scan',
        color: 'from-purple-500 to-pink-500'
      },
      'deep': {
        icon: Zap,
        name: 'Deep Scan',
        color: 'from-orange-500 to-red-500'
      }
    };
    return categoryMap[category] || {
      icon: Target,
      name: 'Unknown Scan',
      color: 'from-gray-500 to-gray-600'
    };
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
            <Target className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">Pentesting</h1>
            <p className="text-gray-300">Comprehensive security scanning and vulnerability assessment</p>
          </div>
        </div>

        {/* Scan Configuration */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left: Scan Categories */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-4">Scan Categories</h3>
            <div className="space-y-3">
              {scanCategories.map((category) => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => {
                      setSelectedCategory(category.id);
                      setSelectedScanType(''); // Reset scan type when category changes
                    }}
                    className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                      selectedCategory === category.id
                        ? 'border-purple-500 bg-purple-600/20'
                        : 'border-gray-600/50 bg-gray-800/30 hover:border-gray-500'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 bg-gradient-to-br ${category.color} rounded-lg flex items-center justify-center`}>
                        <Icon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="text-white font-medium">{category.name}</h4>
                        <p className="text-gray-400 text-sm">{category.description}</p>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Middle: Scan Types */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-4">Scan Types</h3>
            {selectedCategory ? (
              <div className="space-y-2">
                {scanCategories.find(cat => cat.id === selectedCategory)?.types.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => setSelectedScanType(type.id)}
                    className={`w-full p-3 rounded-lg border transition-all duration-200 text-left ${
                      selectedScanType === type.id
                        ? 'border-cyan-500 bg-cyan-600/20 text-cyan-300'
                        : 'border-gray-600/50 bg-gray-800/30 hover:border-gray-500 text-gray-300'
                    }`}
                  >
                    <h5 className="font-medium">{type.name}</h5>
                    <p className="text-sm opacity-80">{type.description}</p>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400">
                <Target className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>Select a scan category first</p>
              </div>
            )}
          </div>

          {/* Right: Target Configuration */}
          <div>
            <h3 className="text-xl font-semibold text-white mb-4">Target Configuration</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {selectedCategory === 'web' ? 'Target URL' : 'Target (IP/Domain/Range)'}
                </label>
                <input
                  type="text"
                  value={target}
                  onChange={(e) => setTarget(e.target.value)}
                  placeholder={
                    selectedCategory === 'web'
                      ? "https://example.com or http://***********"
                      : "*********** or scanme.nmap.org or ***********/24"
                  }
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                />
              </div>

              {(selectedCategory === 'network' || selectedCategory === 'deep') && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Ports (comma-separated)
                  </label>
                  <input
                    type="text"
                    value={ports}
                    onChange={(e) => setPorts(e.target.value)}
                    placeholder="80,443,22,21 or leave empty for default"
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              )}

              {/* Scan Summary */}
              {selectedCategory && selectedScanType && (
                <div className="bg-gray-800/30 border border-gray-600/30 rounded-xl p-4">
                  <h4 className="text-white font-medium mb-2">Scan Summary</h4>
                  <div className="space-y-1 text-sm">
                    <p className="text-gray-300">
                      <span className="text-purple-400">Category:</span> {scanCategories.find(c => c.id === selectedCategory)?.name}
                    </p>
                    <p className="text-gray-300">
                      <span className="text-purple-400">Type:</span> {scanCategories.find(c => c.id === selectedCategory)?.types.find(t => t.id === selectedScanType)?.name}
                    </p>
                    <p className="text-gray-300">
                      <span className="text-purple-400">Target:</span> {target || 'Not specified'}
                    </p>
                    {ports && (
                      <p className="text-gray-300">
                        <span className="text-purple-400">Ports:</span> {ports}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {selectedCategory === 'web' && (
                <div className="bg-green-600/10 border border-green-500/20 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Zap className="w-5 h-5 text-green-400" />
                    <span className="text-green-400 font-medium">Robust Scan Technology</span>
                  </div>
                  <p className="text-gray-300 text-sm">
                    Web scans now use our robust scan technology that runs in the background and preserves logs even during page refreshes.
                    Your scan will continue running independently and you can monitor progress in real-time.
                  </p>
                </div>
              )}

              <button
                onClick={handleStartScan}
                disabled={!target || !selectedCategory || !selectedScanType || isScanning}
                className="w-full flex items-center justify-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                {isScanning ? (
                  <>
                    <Pause className="w-5 h-5 animate-spin" />
                    <span>Scanning...</span>
                  </>
                ) : (
                  <>
                    <Play className="w-5 h-5" />
                    <span>Start {selectedCategory ? scanCategories.find(c => c.id === selectedCategory)?.name : 'Scan'}</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* Running Scans Monitor */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
              <Activity className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">Running Scans</h2>
              <p className="text-gray-300">Monitor active scans in real-time</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => {
                if (showRunningScans) {
                  stopRunningScansPolling();
                } else {
                  loadRunningScans();
                  startRunningScansPolling();
                }
                setShowRunningScans(!showRunningScans);
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Activity className="w-4 h-4" />
              <span>{showRunningScans ? 'Hide Monitor' : 'Show Monitor'}</span>
            </button>
            {showRunningScans && (
              <button
                onClick={loadRunningScans}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Refresh</span>
              </button>
            )}
          </div>
        </div>

        {showRunningScans && (
          <div className="space-y-4">
            {runningScans.length === 0 ? (
              <div className="text-center py-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-800/50 rounded-full mb-4">
                  <Activity className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">No Running Scans</h3>
                <p className="text-gray-400">All scans have completed or no scans are currently active</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* List of running scans */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Active Scans ({runningScans.length})</h3>
                  {runningScans.map((scan) => (
                    <div
                      key={scan.scan_id}
                      className={`bg-gray-800/50 border rounded-xl p-4 cursor-pointer transition-all duration-200 ${
                        selectedRunningScan === scan.scan_id
                          ? 'border-blue-500 bg-blue-600/20'
                          : 'border-gray-600/30 hover:border-gray-500'
                      }`}
                      onClick={() => loadRunningScanDetails(scan.scan_id)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{getCategoryIcon(scan.category)}</span>
                          <div>
                            <div className="flex items-center space-x-2">
                              <span className="text-white font-medium">
                                {scan.category.charAt(0).toUpperCase() + scan.category.slice(1)} Scan
                              </span>
                              <span className="text-gray-400">•</span>
                              <span className="text-cyan-400 text-sm">{scan.scan_type}</span>
                            </div>
                            <p className="text-gray-300 text-sm">{scan.target}</p>
                            {user?.role === 'admin' && scan.user_id && (
                              <p className="text-yellow-400 text-xs flex items-center">
                                <span className="mr-1">👤</span>
                                {scan.user_id}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center space-x-2 mb-1">
                            <Clock className="w-4 h-4 text-yellow-400 animate-spin" />
                            <span className="text-yellow-400 text-sm font-medium">Running</span>
                          </div>
                          <p className="text-gray-400 text-xs mb-2">
                            {scan.current_duration_formatted || scan.current_duration}
                          </p>
                          <div className="flex space-x-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStopScan(scan.scan_id);
                              }}
                              className="px-2 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded transition-colors"
                              title="Stop scan"
                            >
                              🛑
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCancelScan(scan.scan_id);
                              }}
                              className="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                              title="Cancel scan"
                            >
                              ❌
                            </button>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400 text-sm">Current Tool:</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{getToolIcon(scan.current_tool)}</span>
                            <span className="text-white text-sm font-medium">{scan.current_tool}</span>
                          </div>
                        </div>

                        {scan.overall_progress !== undefined && (
                          <div>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-gray-400 text-sm">Progress:</span>
                              <span className="text-blue-400 text-sm font-medium">{scan.overall_progress}%</span>
                            </div>
                            <div className="w-full bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${scan.overall_progress}%` }}
                              />
                            </div>
                          </div>
                        )}

                        {scan.tools_status && (
                          <div className="flex items-center space-x-1 mt-2">
                            <span className="text-gray-400 text-xs">Tools:</span>
                            <div className="flex space-x-1">
                              {Object.entries(scan.tools_status).map(([tool, status]: [string, any]) => (
                                <div
                                  key={tool}
                                  className={`w-6 h-6 rounded text-xs flex items-center justify-center font-bold ${getStatusColor(status.status)} ${
                                    status.status === 'completed' ? 'bg-green-600/20' :
                                    status.status === 'running' ? 'bg-blue-600/20' :
                                    status.status === 'failed' ? 'bg-red-600/20' :
                                    'bg-gray-600/20'
                                  }`}
                                  title={`${tool}: ${status.status} (${status.progress}%)`}
                                >
                                  {tool.charAt(0).toUpperCase()}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Détails du scan sélectionné */}
                <div className="bg-gray-800/30 border border-gray-600/20 rounded-xl p-6">
                  {selectedRunningScan && selectedScanDetails ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                          <Settings className="w-5 h-5" />
                          <span>Scan Details</span>
                        </h3>
                        <div className="flex space-x-2">
                          {/* Show Logs button - only visible to admin users */}
                          {isAdmin && (
                            <button
                              onClick={() => {
                                if (showLogs) {
                                  setShowLogs(false);
                                  stopLogsPolling();
                                } else {
                                  setShowLogs(true);
                                  loadScanLogs(selectedScanDetails.scan_id);
                                  startLogsPolling(selectedScanDetails.scan_id);
                                }
                              }}
                              className={`px-3 py-1 text-white text-sm rounded transition-colors flex items-center space-x-1 ${
                                showLogs ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gray-600 hover:bg-gray-700'
                              }`}
                              title="Toggle logs"
                            >
                              <span>📋</span>
                              <span>{showLogs ? 'Hide Logs' : 'Show Logs'}</span>
                            </button>
                          )}
                          <button
                            onClick={() => handleStopScan(selectedScanDetails.scan_id)}
                            className="px-3 py-1 bg-orange-600 hover:bg-orange-700 text-white text-sm rounded transition-colors flex items-center space-x-1"
                            title="Stop scan"
                          >
                            <span>🛑</span>
                            <span>Stop</span>
                          </button>
                          <button
                            onClick={() => handleCancelScan(selectedScanDetails.scan_id)}
                            className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors flex items-center space-x-1"
                            title="Cancel scan"
                          >
                            <span>❌</span>
                            <span>Cancel</span>
                          </button>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-400">Scan ID:</span>
                          <p className="text-white font-mono text-xs">{selectedScanDetails.scan_id}</p>
                        </div>
                        <div>
                          <span className="text-gray-400">Started:</span>
                          <p className="text-white">{new Date(selectedScanDetails.start_time).toLocaleTimeString()}</p>
                        </div>
                        <div>
                          <span className="text-gray-400">Category:</span>
                          <p className="text-white">{selectedScanDetails.category}</p>
                        </div>
                        <div>
                          <span className="text-gray-400">Type:</span>
                          <p className="text-white">{selectedScanDetails.scan_type}</p>
                        </div>
                      </div>

                      {selectedScanDetails.tools_status && (
                        <div className="space-y-3">
                          <h4 className="text-white font-medium">Tools Status</h4>
                          {Object.entries(selectedScanDetails.tools_status).map(([tool, status]: [string, any]) => (
                            <div key={tool} className="bg-gray-700/30 rounded-lg p-3">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-2">
                                  <span className="text-lg">{getToolIcon(tool)}</span>
                                  <span className="text-white font-medium">{tool.toUpperCase()}</span>
                                </div>
                                <span className={`text-sm font-medium ${getStatusColor(status.status)}`}>
                                  {status.status}
                                </span>
                              </div>

                              <div className="flex items-center space-x-2">
                                <div className="flex-1 bg-gray-600 rounded-full h-2">
                                  <div
                                    className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${status.progress}%` }}
                                  />
                                </div>
                                <span className="text-xs text-gray-400">{status.progress}%</span>
                              </div>

                              {status.start_time && (
                                <p className="text-xs text-gray-400 mt-1">
                                  Started: {new Date(status.start_time).toLocaleTimeString()}
                                </p>
                              )}

                              {status.error && (
                                <p className="text-xs text-red-400 mt-1">
                                  Error: {status.error}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Logs section - only visible to admin users */}
                      {isAdmin && showLogs && (
                        <div className="space-y-3 mt-6">
                          <div className="flex items-center justify-between">
                            <h4 className="text-white font-medium flex items-center space-x-2">
                              <span>📋</span>
                              <span>Live Logs</span>
                            </h4>
                            <button
                              onClick={() => loadScanLogs(selectedScanDetails.scan_id)}
                              className="px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded transition-colors"
                            >
                              🔄 Refresh
                            </button>
                          </div>

                          <div className="bg-black/50 rounded-lg p-3 max-h-64 overflow-y-auto font-mono text-sm">
                            {scanLogs.length === 0 ? (
                              <div className="text-gray-400 text-center py-4">
                                <div className="mb-2">📋</div>
                                <div className="text-sm">
                                  {selectedScanDetails.status === 'running' ? (
                                    <>
                                      <p>Waiting for logs...</p>
                                      <p className="text-xs mt-1">Logs will appear as tools execute</p>
                                    </>
                                  ) : (
                                    <>
                                      <p>No logs available</p>
                                      <p className="text-xs mt-1">This scan was created before logging was implemented</p>
                                    </>
                                  )}
                                </div>
                              </div>
                            ) : (
                              <div className="space-y-1">
                                {scanLogs.slice().reverse().map((log, index) => (
                                  <div key={index} className="flex items-start space-x-2 text-xs">
                                    <span className="text-gray-500 min-w-[60px]">
                                      {formatLogTime(log.timestamp)}
                                    </span>
                                    <span className="text-lg leading-none">
                                      {getLogIcon(log.event_type)}
                                    </span>
                                    <span className={`${getLogLevelColor(log.level)} font-medium min-w-[50px]`}>
                                      {log.level.toUpperCase()}
                                    </span>
                                    <span className="text-gray-300 flex-1">
                                      {log.message}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>

                          {scanLogs.length > 0 && (
                            <div className="text-xs text-gray-400 text-center">
                              Showing {scanLogs.length} recent log entries • Auto-refreshing every 3s
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Settings className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-400">Select a running scan to view details</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Scan Results */}
      <div id="scan-history-section" className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-white">Scan History</h2>
            {user?.role === 'admin' && (
              <span className="px-3 py-1 bg-yellow-600/20 border border-yellow-500/30 rounded-lg text-yellow-400 text-sm font-medium">
                👑 Admin View (All Scans)
              </span>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <p className="text-gray-400 text-sm">Click refresh to see latest results</p>

            {/* Bouton Export All CSV */}
            {scanResults.length > 0 && (
              <button
                onClick={handleExportAllCSV}
                disabled={exportingCSV}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
                title="Exporter tous les scans en CSV"
              >
                {exportingCSV ? (
                  <>
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    <span>Export...</span>
                  </>
                ) : (
                  <>
                    <FileDown className="w-4 h-4" />
                    <span>Export CSV</span>
                  </>
                )}
              </button>
            )}

            <button
              onClick={loadScanHistory}
              disabled={loadingHistory}
              className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 disabled:opacity-50 text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              <RefreshCw className={`w-4 h-4 ${loadingHistory ? 'animate-spin' : ''}`} />
              <span>Refresh History</span>
            </button>
          </div>
        </div>

        {/* Filters Section */}
        {scanResults.length > 0 && (
          <div className="bg-gradient-to-br from-gray-800/40 to-gray-900/60 backdrop-blur-sm border border-gray-600/30 rounded-2xl p-6 mb-6 shadow-xl">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                  <Search className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">Filter Scans</h3>
                  <p className="text-gray-400 text-sm">Refine your scan history view</p>
                </div>
              </div>
              <button
                onClick={resetFilters}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-red-500/20 to-orange-500/20 hover:from-red-500/30 hover:to-orange-500/30 border border-red-500/30 text-red-300 hover:text-red-200 rounded-xl transition-all duration-200 transform hover:scale-105"
              >
                <X className="w-4 h-4" />
                <span>Reset All</span>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Category Filter */}
              <div className="space-y-3">
                <label className="flex items-center space-x-2 text-sm font-semibold text-gray-200">
                  <Network className="w-4 h-4 text-purple-400" />
                  <span>Category</span>
                </label>
                <div className="relative">
                  <select
                    value={filterCategory}
                    onChange={(e) => {
                      setFilterCategory(e.target.value);
                      setCurrentPage(1); // Reset to first page when filter changes
                    }}
                    className="w-full px-4 py-3 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 transition-all duration-200 appearance-none cursor-pointer hover:bg-gray-700/70"
                  >
                    <option value="all" className="bg-gray-800">🔍 All Categories</option>
                    {getFilterOptions().categories.map(category => (
                      <option key={category} value={category} className="bg-gray-800">
                        {getCategoryIcon(category)} {getScanCategoryInfo(category).name}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Status Filter */}
              <div className="space-y-3">
                <label className="flex items-center space-x-2 text-sm font-semibold text-gray-200">
                  <Activity className="w-4 h-4 text-cyan-400" />
                  <span>Status</span>
                </label>
                <div className="relative">
                  <select
                    value={filterStatus}
                    onChange={(e) => {
                      setFilterStatus(e.target.value);
                      setCurrentPage(1); // Reset to first page when filter changes
                    }}
                    className="w-full px-4 py-3 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500/50 transition-all duration-200 appearance-none cursor-pointer hover:bg-gray-700/70"
                  >
                    <option value="all" className="bg-gray-800">📊 All Statuses</option>
                    {getFilterOptions().statuses.map(status => (
                      <option key={status} value={status} className="bg-gray-800">
                        {status === 'running' && '🔄'}
                        {status === 'completed' && '✅'}
                        {status === 'failed' && '❌'}
                        {status === 'stopped' && '⏸️'}
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Scan Type Filter */}
              <div className="space-y-3">
                <label className="flex items-center space-x-2 text-sm font-semibold text-gray-200">
                  <Settings className="w-4 h-4 text-green-400" />
                  <span>Scan Type</span>
                </label>
                <div className="relative">
                  <select
                    value={filterScanType}
                    onChange={(e) => {
                      setFilterScanType(e.target.value);
                      setCurrentPage(1); // Reset to first page when filter changes
                    }}
                    className="w-full px-4 py-3 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500/50 transition-all duration-200 appearance-none cursor-pointer hover:bg-gray-700/70"
                  >
                    <option value="all" className="bg-gray-800">⚙️ All Types</option>
                    {getFilterOptions().scanTypes.map(scanType => (
                      <option key={scanType} value={scanType} className="bg-gray-800">
                        {scanType === 'basic' && '🟢'}
                        {scanType === 'aggressive' && '🔴'}
                        {scanType === 'stealth' && '🟡'}
                        {scanType === 'comprehensive' && '🟣'}
                        {scanType.charAt(0).toUpperCase() + scanType.slice(1)}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* User Filter (Admin only) */}
              {isAdmin && (
                <div className="space-y-3">
                  <label className="flex items-center space-x-2 text-sm font-semibold text-gray-200">
                    <span className="w-4 h-4 text-yellow-400">👤</span>
                    <span>User</span>
                  </label>
                  <div className="relative">
                    <select
                      value={filterUser}
                      onChange={(e) => {
                        setFilterUser(e.target.value);
                        setCurrentPage(1); // Reset to first page when filter changes
                      }}
                      className="w-full px-4 py-3 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl text-white text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500/50 transition-all duration-200 appearance-none cursor-pointer hover:bg-gray-700/70"
                    >
                      <option value="all" className="bg-gray-800">👥 All Users</option>
                      {getFilterOptions().users.map(user => (
                        <option key={user} value={user} className="bg-gray-800">
                          👤 {user}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Filter Summary */}
            <div className="mt-6 pt-4 border-t border-gray-600/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 px-3 py-1 bg-purple-600/20 border border-purple-500/30 rounded-lg">
                    <span className="text-purple-400 text-sm font-medium">
                      📊 {(() => {
                        const paginationInfo = getPaginationInfo();
                        return `${paginationInfo.startIndex}-${paginationInfo.endIndex} of ${paginationInfo.totalScans} scans`;
                      })()}
                    </span>
                  </div>
                  {(filterCategory !== 'all' || filterStatus !== 'all' || filterScanType !== 'all' || (isAdmin && filterUser !== 'all')) && (
                    <div className="flex items-center space-x-2 px-3 py-1 bg-cyan-600/20 border border-cyan-500/30 rounded-lg">
                      <span className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></span>
                      <span className="text-cyan-400 text-sm font-medium">Filters Active</span>
                    </div>
                  )}
                </div>

                {/* Active Filter Tags */}
                <div className="flex items-center space-x-2">
                  {filterCategory !== 'all' && (
                    <span className="inline-flex items-center space-x-1 px-2 py-1 bg-purple-600/20 border border-purple-500/30 rounded-md text-purple-300 text-xs">
                      <span>{getCategoryIcon(filterCategory)}</span>
                      <span>{getScanCategoryInfo(filterCategory).name}</span>
                      <button onClick={() => {
                        setFilterCategory('all');
                        setCurrentPage(1);
                      }} className="hover:text-purple-200">
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  )}
                  {filterStatus !== 'all' && (
                    <span className="inline-flex items-center space-x-1 px-2 py-1 bg-cyan-600/20 border border-cyan-500/30 rounded-md text-cyan-300 text-xs">
                      <span>
                        {filterStatus === 'running' && '🔄'}
                        {filterStatus === 'completed' && '✅'}
                        {filterStatus === 'failed' && '❌'}
                        {filterStatus === 'stopped' && '⏸️'}
                      </span>
                      <span>{filterStatus.charAt(0).toUpperCase() + filterStatus.slice(1)}</span>
                      <button onClick={() => {
                        setFilterStatus('all');
                        setCurrentPage(1);
                      }} className="hover:text-cyan-200">
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  )}
                  {filterScanType !== 'all' && (
                    <span className="inline-flex items-center space-x-1 px-2 py-1 bg-green-600/20 border border-green-500/30 rounded-md text-green-300 text-xs">
                      <span>
                        {filterScanType === 'basic' && '🟢'}
                        {filterScanType === 'aggressive' && '🔴'}
                        {filterScanType === 'stealth' && '🟡'}
                        {filterScanType === 'comprehensive' && '🟣'}
                      </span>
                      <span>{filterScanType.charAt(0).toUpperCase() + filterScanType.slice(1)}</span>
                      <button onClick={() => {
                        setFilterScanType('all');
                        setCurrentPage(1);
                      }} className="hover:text-green-200">
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  )}
                  {isAdmin && filterUser !== 'all' && (
                    <span className="inline-flex items-center space-x-1 px-2 py-1 bg-yellow-600/20 border border-yellow-500/30 rounded-md text-yellow-300 text-xs">
                      <span>👤</span>
                      <span>{filterUser}</span>
                      <button onClick={() => {
                        setFilterUser('all');
                        setCurrentPage(1);
                      }} className="hover:text-yellow-200">
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {loadingHistory ? (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-600/20 rounded-full mb-4">
              <RefreshCw className="w-8 h-8 text-purple-400 animate-spin" />
            </div>
            <p className="text-gray-400">Loading scan history...</p>
          </div>
        ) : scanResults.length === 0 ? (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-800/50 rounded-full mb-4">
              <Target className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No scans in database</h3>
            <p className="text-gray-400">Start your first scan to see results stored in the database</p>
          </div>
        ) : getFilteredScanResults().length === 0 ? (
          <div className="text-center py-16">
            <div className="relative mb-6">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500/20 to-cyan-500/20 rounded-2xl border border-purple-500/30 shadow-xl">
                <Search className="w-10 h-10 text-purple-400" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
                <X className="w-4 h-4 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-3">No scans match your filters</h3>
            <p className="text-gray-400 mb-6 max-w-md mx-auto">
              We couldn't find any scans that match your current filter criteria.
              Try adjusting your filters or reset them to see all available scans.
            </p>
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={resetFilters}
                className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <RefreshCw className="w-5 h-5" />
                <span>Reset All Filters</span>
              </button>
              <button
                onClick={loadScanHistory}
                className="flex items-center space-x-2 px-6 py-3 bg-gray-700/50 hover:bg-gray-700/70 border border-gray-600/50 text-gray-300 hover:text-white rounded-xl font-medium transition-all duration-200"
              >
                <RefreshCw className="w-5 h-5" />
                <span>Refresh Scans</span>
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="space-y-4">
              {getPaginatedScanResults().map((scan) => {
              const categoryInfo = getScanCategoryInfo(scan.category);
              const Icon = categoryInfo.icon;

              return (
                <div
                  key={scan.id}
                  className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6 hover:bg-gray-700/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`w-10 h-10 bg-gradient-to-br ${categoryInfo.color} rounded-lg flex items-center justify-center shadow-lg`}>
                        <Icon className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex flex-col">
                        <div className="flex items-center space-x-2">
                          <span className="text-white font-semibold">{categoryInfo.name}</span>
                          <span className="text-gray-400">•</span>
                          <span className="text-cyan-400 font-medium capitalize">{scan.scan_type}</span>
                          {scan.isRobust && (
                            <span className="bg-green-600/20 border border-green-500/30 rounded-lg px-2 py-0.5 text-green-400 text-xs font-medium">
                              🚀 ROBUST
                            </span>
                          )}
                        </div>
                        <span className="text-gray-300 text-sm">{scan.target}</span>
                      </div>
                      {scan.vulnerabilities && scan.vulnerabilities.length > 0 && (
                        <div className="bg-red-600/20 border border-red-500/30 rounded-lg px-3 py-1">
                          <span className="text-red-400 text-sm font-medium">
                            {scan.vulnerabilities.length} vulns
                          </span>
                        </div>
                      )}
                    </div>
                
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(scan.status)}
                    <span className={`font-medium uppercase ${getStatusColor(scan.status)}`}>
                      {scan.status}
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => loadScanDetails(scan.scan_id)}
                      disabled={loadingScanDetails}
                      className="flex items-center space-x-1 text-purple-400 hover:text-purple-300 disabled:opacity-50 transition-colors"
                    >
                      <Eye className="w-4 h-4" />
                      <span>View</span>
                    </button>
                    {scan.status !== 'running' && (
                      <>
                        {scan.status === 'stopped' && (
                          <button
                            onClick={() => handleRestartScan(scan.scan_id)}
                            className="flex items-center space-x-1 text-green-400 hover:text-green-300 transition-colors"
                            title="Resume/Restart scan"
                          >
                            <RefreshCw className="w-4 h-4" />
                            <span>Resume</span>
                          </button>
                        )}
                        {/* Delete button - only visible to admin users */}
                        {isAdmin && (
                          <button
                            onClick={() => handleDeleteScan(scan.scan_id)}
                            className="flex items-center space-x-1 text-red-400 hover:text-red-300 transition-colors"
                            title="Delete scan"
                          >
                            <X className="w-4 h-4" />
                            <span>Delete</span>
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Started:</span>
                  <p className="text-white font-medium">{new Date(scan.start_time).toLocaleString()}</p>
                </div>
                {scan.end_time && (
                  <div>
                    <span className="text-gray-400">Completed:</span>
                    <p className="text-white font-medium">{new Date(scan.end_time).toLocaleString()}</p>
                  </div>
                )}
                {scan.duration && (
                  <div>
                    <span className="text-gray-400">Duration:</span>
                    <p className="text-white font-medium">{scan.duration}s</p>
                  </div>
                )}
                {user?.role === 'admin' && scan.user_id && (
                  <div>
                    <span className="text-gray-400">User:</span>
                    <p className="text-yellow-400 font-medium flex items-center">
                      <span className="mr-1">👤</span>
                      {scan.user_id}
                    </p>
                  </div>
                )}
                </div>
              </div>
            );
          })}
            </div>

            {/* Pagination Controls */}
            {(() => {
              const paginationInfo = getPaginationInfo();
              if (paginationInfo.totalPages > 1) {
                return (
                  <div className="flex items-center justify-between pt-6 border-t border-gray-600/30">
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span>
                        Showing {paginationInfo.startIndex}-{paginationInfo.endIndex} of {paginationInfo.totalScans} scans
                      </span>
                      <span>•</span>
                      <span>Page {currentPage} of {paginationInfo.totalPages}</span>
                    </div>

                    <div className="flex items-center space-x-2">
                      {/* Previous Button */}
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={!paginationInfo.hasPrevPage}
                        className="flex items-center space-x-2 px-4 py-2 bg-gray-700/50 hover:bg-gray-700/70 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-600/50 text-gray-300 hover:text-white rounded-lg transition-all duration-200"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                        <span>Previous</span>
                      </button>

                      {/* Page Numbers */}
                      <div className="flex items-center space-x-1">
                        {(() => {
                          const pages = [];
                          const maxVisiblePages = 5;
                          let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                          let endPage = Math.min(paginationInfo.totalPages, startPage + maxVisiblePages - 1);

                          // Adjust start page if we're near the end
                          if (endPage - startPage + 1 < maxVisiblePages) {
                            startPage = Math.max(1, endPage - maxVisiblePages + 1);
                          }

                          // First page + ellipsis
                          if (startPage > 1) {
                            pages.push(
                              <button
                                key={1}
                                onClick={() => handlePageChange(1)}
                                className="w-10 h-10 flex items-center justify-center bg-gray-700/50 hover:bg-gray-700/70 border border-gray-600/50 text-gray-300 hover:text-white rounded-lg transition-all duration-200"
                              >
                                1
                              </button>
                            );
                            if (startPage > 2) {
                              pages.push(
                                <span key="ellipsis1" className="px-2 text-gray-500">...</span>
                              );
                            }
                          }

                          // Visible page numbers
                          for (let i = startPage; i <= endPage; i++) {
                            pages.push(
                              <button
                                key={i}
                                onClick={() => handlePageChange(i)}
                                className={`w-10 h-10 flex items-center justify-center border rounded-lg transition-all duration-200 ${
                                  i === currentPage
                                    ? 'bg-gradient-to-r from-purple-600 to-cyan-600 border-purple-500/50 text-white font-medium'
                                    : 'bg-gray-700/50 hover:bg-gray-700/70 border-gray-600/50 text-gray-300 hover:text-white'
                                }`}
                              >
                                {i}
                              </button>
                            );
                          }

                          // Last page + ellipsis
                          if (endPage < paginationInfo.totalPages) {
                            if (endPage < paginationInfo.totalPages - 1) {
                              pages.push(
                                <span key="ellipsis2" className="px-2 text-gray-500">...</span>
                              );
                            }
                            pages.push(
                              <button
                                key={paginationInfo.totalPages}
                                onClick={() => handlePageChange(paginationInfo.totalPages)}
                                className="w-10 h-10 flex items-center justify-center bg-gray-700/50 hover:bg-gray-700/70 border border-gray-600/50 text-gray-300 hover:text-white rounded-lg transition-all duration-200"
                              >
                                {paginationInfo.totalPages}
                              </button>
                            );
                          }

                          return pages;
                        })()}
                      </div>

                      {/* Next Button */}
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={!paginationInfo.hasNextPage}
                        className="flex items-center space-x-2 px-4 py-2 bg-gray-700/50 hover:bg-gray-700/70 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-600/50 text-gray-300 hover:text-white rounded-lg transition-all duration-200"
                      >
                        <span>Next</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </div>
                  </div>
                );
              }
              return null;
            })()}
          </div>
        )}
      </div>

      {/* Modal des détails du scan */}
      {showScanDetails && selectedScanDetails && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 border border-gray-600/30 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            {/* Header de la modal */}
            <div className="flex items-center justify-between p-6 border-b border-gray-600/30">
              <div className="flex items-center space-x-4">
                <div className={`w-10 h-10 bg-gradient-to-br ${getScanCategoryInfo(selectedScanDetails.category).color} rounded-lg flex items-center justify-center shadow-lg`}>
                  {(() => {
                    const Icon = getScanCategoryInfo(selectedScanDetails.category).icon;
                    return <Icon className="w-5 h-5 text-white" />;
                  })()}
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">
                    {getScanCategoryInfo(selectedScanDetails.category).name} - {selectedScanDetails.scan_type}
                  </h2>
                  <p className="text-gray-400">{selectedScanDetails.target}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Bouton Export PDF */}
                {selectedScanDetails.status !== 'running' && (
                  <button
                    onClick={() => handleExportPDF(selectedScanDetails.scan_id)}
                    disabled={exportingPDF}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
                    title="Exporter le rapport en PDF"
                  >
                    {exportingPDF ? (
                      <>
                        <RefreshCw className="w-4 h-4 animate-spin" />
                        <span>Export...</span>
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4" />
                        <span>Export PDF</span>
                      </>
                    )}
                  </button>
                )}

                {/* Bouton Fermer */}
                <button
                  onClick={() => setShowScanDetails(false)}
                  className="w-8 h-8 bg-gray-800 hover:bg-gray-700 rounded-lg flex items-center justify-center transition-colors"
                >
                  <X className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Contenu de la modal */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {/* Informations générales */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Status</h3>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(selectedScanDetails.status)}
                    <span className={`font-medium uppercase ${getStatusColor(selectedScanDetails.status)}`}>
                      {selectedScanDetails.status}
                    </span>
                    {selectedScanDetails.isRobust && (
                      <span className="bg-green-600/20 border border-green-500/30 rounded px-2 py-0.5 text-green-400 text-xs font-medium">
                        🚀 ROBUST
                      </span>
                    )}
                  </div>
                  {selectedScanDetails.progress !== undefined && (
                    <div className="mt-2">
                      <div className="flex justify-between text-xs text-gray-400 mb-1">
                        <span>Progress</span>
                        <span>{selectedScanDetails.status === 'completed' ? '100' : selectedScanDetails.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-600 to-cyan-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${selectedScanDetails.status === 'completed' ? 100 : selectedScanDetails.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Duration</h3>
                  <p className="text-gray-300">
                    {selectedScanDetails.duration ? `${selectedScanDetails.duration}s` :
                     selectedScanDetails.status === 'completed' && selectedScanDetails.end_time && selectedScanDetails.start_time ?
                       `${Math.floor((new Date(selectedScanDetails.end_time).getTime() - new Date(selectedScanDetails.start_time).getTime()) / 1000)}s` :
                     selectedScanDetails.status === 'running' && selectedScanDetails.start_time ?
                       `${Math.floor((new Date().getTime() - new Date(selectedScanDetails.start_time).getTime()) / 1000)}s` :
                       'N/A'}
                  </p>
                  {selectedScanDetails.current_tool && (
                    <p className="text-cyan-400 text-sm mt-1">
                      Current: {selectedScanDetails.current_tool.toUpperCase()}
                    </p>
                  )}
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Ports</h3>
                  <p className="text-green-400 font-bold text-xl">
                    {(() => {
                      let portCount = 0;

                      // Check for summary data in results (for vulnerability scans)
                      if (selectedScanDetails.results?.summary) {
                        if (selectedScanDetails.results.summary.total_ports_scanned !== undefined) {
                          portCount = selectedScanDetails.results.summary.total_ports_scanned;
                        }
                      }

                      // Also check for ports in results.ports
                      if (portCount === 0 && selectedScanDetails.results?.ports) {
                        portCount = selectedScanDetails.results.ports.length;
                      }

                      // Check logs for robust scans
                      if (portCount === 0 && selectedScanDetails.logs) {
                        selectedScanDetails.logs.forEach((log: any) => {
                          if (log.data?.results_summary?.ports) {
                            portCount += log.data.results_summary.ports.length;
                          }
                          if (log.data?.summary) {
                            Object.values(log.data.summary).forEach((toolResult: any) => {
                              if (toolResult?.ports) {
                                portCount += toolResult.ports.length;
                              }
                            });
                          }
                        });
                      }

                      // Also check regular results for non-robust scans
                      if (portCount === 0 && selectedScanDetails.results?.ports) {
                        portCount += selectedScanDetails.results.ports.length;
                      }

                      return portCount;
                    })()}
                  </p>
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Vulnerabilities</h3>
                  <p className="text-red-400 font-bold text-xl">
                    {(() => {
                      let vulnCount = 0;

                      // Check for summary data in results (for vulnerability scans)
                      if (selectedScanDetails.results?.summary) {
                        if (selectedScanDetails.results.summary.total_vulnerabilities !== undefined) {
                          vulnCount = selectedScanDetails.results.summary.total_vulnerabilities;
                        }
                      }

                      // Also check for vulnerabilities in results.vulnerabilities
                      if (vulnCount === 0 && selectedScanDetails.results?.vulnerabilities) {
                        vulnCount = selectedScanDetails.results.vulnerabilities.length;
                      }

                      // Check logs for robust scans
                      if (vulnCount === 0 && selectedScanDetails.logs) {
                        selectedScanDetails.logs.forEach((log: any) => {
                          if (log.data?.results_summary?.vulnerabilities) {
                            vulnCount += log.data.results_summary.vulnerabilities.length;
                          }
                          if (log.data?.summary) {
                            Object.values(log.data.summary).forEach((toolResult: any) => {
                              if (toolResult?.vulnerabilities) {
                                vulnCount += toolResult.vulnerabilities.length;
                              }
                              if (toolResult?.alerts) {
                                vulnCount += toolResult.alerts.length;
                              }
                              if (toolResult?.injections) {
                                vulnCount += toolResult.injections.length;
                              }
                            });
                          }
                        });
                      }

                      // Also check regular results for non-robust scans
                      if (vulnCount === 0 && selectedScanDetails.results) {
                        if (selectedScanDetails.results.nikto?.vulnerabilities) {
                          vulnCount += selectedScanDetails.results.nikto.vulnerabilities.length;
                        }
                        if (selectedScanDetails.results.sqlmap?.injections) {
                          vulnCount += selectedScanDetails.results.sqlmap.injections.length;
                        }
                        if (selectedScanDetails.results.zap?.alerts) {
                          vulnCount += selectedScanDetails.results.zap.alerts.length;
                        }
                      }

                      return vulnCount;
                    })()}
                  </p>
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Directories</h3>
                  <p className="text-blue-400 font-bold text-xl">
                    {(() => {
                      let dirCount = 0;

                      // Check logs for robust scans
                      if (selectedScanDetails.logs) {
                        selectedScanDetails.logs.forEach((log: any) => {
                          if (log.data?.results_summary?.directories) {
                            dirCount += log.data.results_summary.directories.length;
                          }
                          if (log.data?.results_summary?.found_paths) {
                            dirCount += log.data.results_summary.found_paths.length;
                          }
                          if (log.data?.summary) {
                            Object.values(log.data.summary).forEach((toolResult: any) => {
                              if (toolResult?.directories) {
                                dirCount += toolResult.directories.length;
                              }
                              if (toolResult?.found_paths) {
                                dirCount += toolResult.found_paths.length;
                              }
                            });
                          }
                        });
                      }

                      // Also check regular results for non-robust scans
                      if (selectedScanDetails.results) {
                        if (selectedScanDetails.results.dirb?.directories) {
                          dirCount += selectedScanDetails.results.dirb.directories.length;
                        }
                        if (selectedScanDetails.results.gobuster?.found_paths) {
                          dirCount += selectedScanDetails.results.gobuster.found_paths.length;
                        }
                      }

                      return dirCount;
                    })()}
                  </p>
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Tools Used</h3>
                  <div className="flex flex-wrap gap-1">
                    {(() => {
                      let tools: string[] = [];

                      // Check for tools in results (for vulnerability scans)
                      if (selectedScanDetails.results?.tools) {
                        tools = Object.keys(selectedScanDetails.results.tools);
                      }

                      // Fallback to existing tools array
                      if (tools.length === 0 && selectedScanDetails.tools && selectedScanDetails.tools.length > 0) {
                        tools = selectedScanDetails.tools;
                      }

                      return tools.length > 0 ? (
                        tools.map((tool: string, index: number) => (
                          <span key={index} className="px-2 py-1 bg-purple-600/20 text-purple-300 rounded text-xs">
                            {getToolIcon(tool)} {tool}
                          </span>
                        ))
                      ) : (
                        <span className="text-gray-400 text-sm">No tools specified</span>
                      );
                    })()}
                  </div>
                </div>
              </div>

              {/* Vulnerability Scan Results */}
              {!selectedScanDetails.isRobust && selectedScanDetails.category === 'vulnerability' && selectedScanDetails.results && (
                <div className="space-y-6 mb-8">
                  <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
                    <Shield className="w-6 h-6 text-purple-400" />
                    <span>Vulnerability Scan Results</span>
                  </h2>

                  {/* Tool Results Summary */}
                  {(() => {
                    try {
                      // Extract tools data from results
                      const toolsData = selectedScanDetails.results?.tools;
                      if (toolsData) {

                        return (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {Object.entries(toolsData).map(([tool, results]: [string, any]) => (
                              <div key={tool} className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6">
                                <div className="flex items-center space-x-3 mb-4">
                                  <span className="text-2xl">{getToolIcon(tool)}</span>
                                  <div>
                                    <h3 className="text-lg font-semibold text-white">{tool.toUpperCase()}</h3>
                                    <p className="text-gray-400 text-sm">
                                      Status: <span className={`font-medium ${getStatusColor(results.status)}`}>
                                        {results.status}
                                      </span>
                                    </p>
                                  </div>
                                </div>

                                {/* Tool-specific results */}
                                {tool === 'openvas' && results.vulnerabilities && (
                                  <div className="space-y-3">
                                    <h4 className="text-red-400 font-medium">
                                      🚨 Vulnerabilities Found ({results.vulnerabilities.length})
                                    </h4>
                                    <div className="space-y-2">
                                      {results.vulnerabilities.slice(0, 3).map((vuln: any, idx: number) => (
                                        <div key={idx} className={`p-3 rounded text-sm ${
                                          vuln.severity === 'high' ? 'bg-red-600/20 border border-red-500/30' :
                                          vuln.severity === 'medium' ? 'bg-yellow-600/20 border border-yellow-500/30' :
                                          vuln.severity === 'low' ? 'bg-blue-600/20 border border-blue-500/30' :
                                          'bg-gray-600/20 border border-gray-500/30'
                                        }`}>
                                          <div className="font-medium text-white">{vuln.name}</div>
                                          <div className="text-gray-300 text-xs mb-1">{vuln.description}</div>
                                          <div className="flex items-center justify-between">
                                            <span className="text-cyan-400 text-xs">Port: {vuln.port}</span>
                                            <span className={`px-2 py-0.5 rounded text-xs font-medium ${
                                              vuln.severity === 'high' ? 'bg-red-600 text-white' :
                                              vuln.severity === 'medium' ? 'bg-yellow-600 text-white' :
                                              vuln.severity === 'low' ? 'bg-blue-600 text-white' :
                                              'bg-gray-600 text-white'
                                            }`}>
                                              {vuln.severity?.toUpperCase()}
                                            </span>
                                          </div>
                                        </div>
                                      ))}
                                      {results.vulnerabilities.length > 3 && (
                                        <div className="text-gray-400 text-xs">
                                          +{results.vulnerabilities.length - 3} more vulnerabilities
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                )}

                                {tool === 'metasploit' && (
                                  <div className="space-y-3">
                                    <h4 className="text-blue-400 font-medium">
                                      🔧 Modules Executed ({results.total_modules || results.modules_run?.length || 0})
                                    </h4>
                                    {results.modules_run && (
                                      <div className="space-y-1">
                                        {results.modules_run.slice(0, 3).map((module: string, idx: number) => (
                                          <div key={idx} className="p-2 bg-blue-600/20 border border-blue-500/30 rounded text-sm">
                                            <div className="font-medium text-white text-xs">{module}</div>
                                          </div>
                                        ))}
                                        {results.modules_run.length > 3 && (
                                          <div className="text-gray-400 text-xs">
                                            +{results.modules_run.length - 3} more modules
                                          </div>
                                        )}
                                      </div>
                                    )}
                                    {results.vulnerabilities && results.vulnerabilities.length === 0 && (
                                      <div className="text-gray-400 text-sm">No exploitable vulnerabilities found</div>
                                    )}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        );
                      }
                    } catch (e) {
                      console.log('Error parsing tools data:', e);
                      return (
                        <div className="text-center py-8 text-gray-400">
                          <AlertTriangle className="w-12 h-12 mx-auto mb-3 opacity-50" />
                          <p>Error parsing tool results</p>
                        </div>
                      );
                    }
                  })()}
                </div>
              )}

              {/* Robust Scan Results */}
              {selectedScanDetails.isRobust && selectedScanDetails.logs && (
                <div className="space-y-6 mb-8">
                  <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
                    <Zap className="w-6 h-6 text-green-400" />
                    <span>Robust Scan Results</span>
                  </h2>

                  {/* Tool Results Summary */}
                  {(() => {
                    const toolResults: any = {};
                    selectedScanDetails.logs.forEach((log: any) => {
                      if (log.event_type === 'tool_result' && log.data?.results_summary) {
                        toolResults[log.data.tool] = log.data.results_summary;
                      }
                      if (log.data?.summary) {
                        Object.assign(toolResults, log.data.summary);
                      }
                    });

                    return Object.keys(toolResults).length > 0 && (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {Object.entries(toolResults).map(([tool, results]: [string, any]) => (
                          <div key={tool} className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6">
                            <div className="flex items-center space-x-3 mb-4">
                              <span className="text-2xl">{getToolIcon(tool)}</span>
                              <div>
                                <h3 className="text-lg font-semibold text-white">{tool.toUpperCase()}</h3>
                                <p className="text-gray-400 text-sm">
                                  {results.scan_time || 'Completed'}
                                </p>
                              </div>
                            </div>

                            {/* Ports (for network scans) */}
                            {results.ports && results.ports.length > 0 && (
                              <div className="mb-4">
                                <h4 className="text-green-400 font-medium mb-2">
                                  🔌 Open Ports ({results.ports.length})
                                </h4>
                                <div className="space-y-1">
                                  {results.ports.slice(0, 5).map((port: any, idx: number) => (
                                    <div key={idx} className="p-2 bg-green-600/20 border border-green-500/30 rounded text-sm">
                                      <div className="font-medium text-white">
                                        Port {port.port}/{port.protocol} - {port.service}
                                      </div>
                                      {port.version && (
                                        <div className="text-gray-300 text-xs">{port.version}</div>
                                      )}
                                    </div>
                                  ))}
                                  {results.ports.length > 5 && (
                                    <div className="text-gray-400 text-xs">
                                      +{results.ports.length - 5} more ports
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Vulnerabilities */}
                            {results.vulnerabilities && results.vulnerabilities.length > 0 && (
                              <div className="mb-4">
                                <h4 className="text-red-400 font-medium mb-2">
                                  🚨 Vulnerabilities ({results.vulnerabilities.length})
                                </h4>
                                <div className="space-y-2">
                                  {results.vulnerabilities.slice(0, 3).map((vuln: any, idx: number) => (
                                    <div key={idx} className={`p-2 rounded text-sm ${
                                      vuln.severity === 'high' ? 'bg-red-600/20 border border-red-500/30' :
                                      vuln.severity === 'medium' ? 'bg-yellow-600/20 border border-yellow-500/30' :
                                      'bg-blue-600/20 border border-blue-500/30'
                                    }`}>
                                      <div className="font-medium text-white">{vuln.type}</div>
                                      <div className="text-gray-300 text-xs">{vuln.description}</div>
                                    </div>
                                  ))}
                                  {results.vulnerabilities.length > 3 && (
                                    <div className="text-gray-400 text-xs">
                                      +{results.vulnerabilities.length - 3} more vulnerabilities
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* SQL Injections */}
                            {results.injections && results.injections.length > 0 && (
                              <div className="mb-4">
                                <h4 className="text-red-400 font-medium mb-2">
                                  💉 SQL Injections ({results.injections.length})
                                </h4>
                                <div className="space-y-2">
                                  {results.injections.map((inj: any, idx: number) => (
                                    <div key={idx} className="p-2 bg-red-600/20 border border-red-500/30 rounded text-sm">
                                      <div className="font-medium text-white">{inj.parameter}</div>
                                      <div className="text-gray-300 text-xs">{inj.type} - {inj.dbms}</div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Directories */}
                            {results.directories && results.directories.length > 0 && (
                              <div className="mb-4">
                                <h4 className="text-blue-400 font-medium mb-2">
                                  📁 Directories ({results.directories.length})
                                </h4>
                                <div className="space-y-1">
                                  {results.directories.slice(0, 3).map((dir: any, idx: number) => (
                                    <div key={idx} className="p-2 bg-blue-600/20 border border-blue-500/30 rounded text-sm">
                                      <div className="font-medium text-white">{dir.path}</div>
                                      <div className="text-gray-300 text-xs">Status: {dir.status}</div>
                                    </div>
                                  ))}
                                  {results.directories.length > 3 && (
                                    <div className="text-gray-400 text-xs">
                                      +{results.directories.length - 3} more directories
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Found Paths */}
                            {results.found_paths && results.found_paths.length > 0 && (
                              <div className="mb-4">
                                <h4 className="text-blue-400 font-medium mb-2">
                                  🔍 Found Paths ({results.found_paths.length})
                                </h4>
                                <div className="space-y-1">
                                  {results.found_paths.slice(0, 3).map((path: any, idx: number) => (
                                    <div key={idx} className="p-2 bg-blue-600/20 border border-blue-500/30 rounded text-sm">
                                      <div className="font-medium text-white">{path.path}</div>
                                      <div className="text-gray-300 text-xs">Status: {path.status}</div>
                                    </div>
                                  ))}
                                  {results.found_paths.length > 3 && (
                                    <div className="text-gray-400 text-xs">
                                      +{results.found_paths.length - 3} more paths
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* ZAP Alerts */}
                            {results.alerts && results.alerts.length > 0 && (
                              <div className="mb-4">
                                <h4 className="text-orange-400 font-medium mb-2">
                                  ⚠️ Security Alerts ({results.alerts.length})
                                </h4>
                                <div className="space-y-1">
                                  {results.alerts.map((alert: any, idx: number) => (
                                    <div key={idx} className={`p-2 rounded text-sm ${
                                      alert.risk === 'High' ? 'bg-red-600/20 border border-red-500/30' :
                                      alert.risk === 'Medium' ? 'bg-yellow-600/20 border border-yellow-500/30' :
                                      'bg-blue-600/20 border border-blue-500/30'
                                    }`}>
                                      <div className="font-medium text-white">{alert.name}</div>
                                      <div className="text-gray-300 text-xs">Risk: {alert.risk} ({alert.count} instances)</div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Tests Count */}
                            {results.total_tests && (
                              <div className="text-gray-400 text-xs">
                                Total tests: {results.total_tests.toLocaleString()}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    );
                  })()}

                  {/* Real-time Logs */}
                  <div className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold text-white flex items-center space-x-2">
                        <Activity className="w-5 h-5" />
                        <span>Real-time Scan Logs</span>
                      </h3>
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-400 text-sm">
                          {selectedScanDetails.logs.length} log entries
                        </span>
                        <button
                          onClick={() => {
                            if (selectedScanDetails.scan_id) {
                              getRobustScanLogs(selectedScanDetails.scan_id).then(data => {
                                setSelectedScanDetails(prev => ({
                                  ...prev,
                                  logs: data.logs
                                }));
                              });
                            }
                          }}
                          className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors"
                        >
                          🔄 Refresh
                        </button>
                      </div>
                    </div>

                    <div className="bg-black/50 rounded-lg p-4 max-h-96 overflow-y-auto font-mono text-sm">
                      {selectedScanDetails.logs.length === 0 ? (
                        <div className="text-gray-400 text-center py-8">
                          <Activity className="w-8 h-8 mx-auto mb-2" />
                          <p>No logs available</p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {selectedScanDetails.logs.slice().reverse().map((log: any, index: number) => (
                            <div key={index} className={`p-2 rounded ${
                              log.level === 'error' ? 'bg-red-900/30 border-l-4 border-red-500' :
                              log.level === 'info' ? 'bg-blue-900/30 border-l-4 border-blue-500' :
                              log.level === 'debug' ? 'bg-gray-900/30 border-l-4 border-gray-500' :
                              'bg-gray-800/30'
                            }`}>
                              <div className="flex items-start space-x-2">
                                <span className="text-gray-400 text-xs whitespace-nowrap">
                                  {new Date(log.timestamp).toLocaleTimeString()}
                                </span>
                                <span className={`text-xs px-1 rounded ${
                                  log.level === 'error' ? 'bg-red-600 text-white' :
                                  log.level === 'info' ? 'bg-blue-600 text-white' :
                                  log.level === 'debug' ? 'bg-gray-600 text-white' :
                                  'bg-gray-700 text-gray-300'
                                }`}>
                                  {log.level.toUpperCase()}
                                </span>
                                <span className="text-gray-300 flex-1 break-words">
                                  {log.message}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Regular Scan Results (for non-robust scans) */}
              {!selectedScanDetails.isRobust && selectedScanDetails.results && (
                <div className="space-y-6 mb-8">
                  <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
                    <Activity className="w-6 h-6 text-blue-400" />
                    <span>Scan Results</span>
                  </h2>

                  {/* Tool Results */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Object.entries(selectedScanDetails.results).map(([tool, results]: [string, any]) => (
                      <div key={tool} className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6">
                        <div className="flex items-center space-x-3 mb-4">
                          <span className="text-2xl">{getToolIcon(tool)}</span>
                          <div>
                            <h3 className="text-lg font-semibold text-white">{tool.toUpperCase()}</h3>
                            <p className="text-gray-400 text-sm">
                              {results.scan_time || 'Completed'}
                            </p>
                          </div>
                        </div>

                        {/* Vulnerabilities */}
                        {results.vulnerabilities && results.vulnerabilities.length > 0 && (
                          <div className="mb-4">
                            <h4 className="text-red-400 font-medium mb-2">
                              🚨 Vulnerabilities ({results.vulnerabilities.length})
                            </h4>
                            <div className="space-y-2">
                              {results.vulnerabilities.slice(0, 3).map((vuln: any, idx: number) => (
                                <div key={idx} className={`p-2 rounded text-sm ${
                                  vuln.severity === 'high' ? 'bg-red-600/20 border border-red-500/30' :
                                  vuln.severity === 'medium' ? 'bg-yellow-600/20 border border-yellow-500/30' :
                                  'bg-blue-600/20 border border-blue-500/30'
                                }`}>
                                  <div className="font-medium text-white">{vuln.type}</div>
                                  <div className="text-gray-300 text-xs">{vuln.description}</div>
                                </div>
                              ))}
                              {results.vulnerabilities.length > 3 && (
                                <div className="text-gray-400 text-xs">
                                  +{results.vulnerabilities.length - 3} more vulnerabilities
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* SQL Injections */}
                        {results.injections && results.injections.length > 0 && (
                          <div className="mb-4">
                            <h4 className="text-red-400 font-medium mb-2">
                              💉 SQL Injections ({results.injections.length})
                            </h4>
                            <div className="space-y-2">
                              {results.injections.map((inj: any, idx: number) => (
                                <div key={idx} className="p-2 bg-red-600/20 border border-red-500/30 rounded text-sm">
                                  <div className="font-medium text-white">{inj.parameter}</div>
                                  <div className="text-gray-300 text-xs">{inj.type} - {inj.dbms}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Directories */}
                        {results.directories && results.directories.length > 0 && (
                          <div className="mb-4">
                            <h4 className="text-blue-400 font-medium mb-2">
                              📁 Directories ({results.directories.length})
                            </h4>
                            <div className="space-y-1">
                              {results.directories.slice(0, 3).map((dir: any, idx: number) => (
                                <div key={idx} className="p-2 bg-blue-600/20 border border-blue-500/30 rounded text-sm">
                                  <div className="font-medium text-white">{dir.path}</div>
                                  <div className="text-gray-300 text-xs">Status: {dir.status}</div>
                                </div>
                              ))}
                              {results.directories.length > 3 && (
                                <div className="text-gray-400 text-xs">
                                  +{results.directories.length - 3} more directories
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Found Paths */}
                        {results.found_paths && results.found_paths.length > 0 && (
                          <div className="mb-4">
                            <h4 className="text-blue-400 font-medium mb-2">
                              🔍 Found Paths ({results.found_paths.length})
                            </h4>
                            <div className="space-y-1">
                              {results.found_paths.slice(0, 3).map((path: any, idx: number) => (
                                <div key={idx} className="p-2 bg-blue-600/20 border border-blue-500/30 rounded text-sm">
                                  <div className="font-medium text-white">{path.path}</div>
                                  <div className="text-gray-300 text-xs">Status: {path.status}</div>
                                </div>
                              ))}
                              {results.found_paths.length > 3 && (
                                <div className="text-gray-400 text-xs">
                                  +{results.found_paths.length - 3} more paths
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* ZAP Alerts */}
                        {results.alerts && results.alerts.length > 0 && (
                          <div className="mb-4">
                            <h4 className="text-orange-400 font-medium mb-2">
                              ⚠️ Security Alerts ({results.alerts.length})
                            </h4>
                            <div className="space-y-1">
                              {results.alerts.map((alert: any, idx: number) => (
                                <div key={idx} className={`p-2 rounded text-sm ${
                                  alert.risk === 'High' ? 'bg-red-600/20 border border-red-500/30' :
                                  alert.risk === 'Medium' ? 'bg-yellow-600/20 border border-yellow-500/30' :
                                  'bg-blue-600/20 border border-blue-500/30'
                                }`}>
                                  <div className="font-medium text-white">{alert.name}</div>
                                  <div className="text-gray-300 text-xs">Risk: {alert.risk} ({alert.count} instances)</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Tests Count */}
                        {results.total_tests && (
                          <div className="text-gray-400 text-xs">
                            Total tests: {results.total_tests.toLocaleString()}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Résultats détaillés */}
              <div className="space-y-6">
                {/* Ports ouverts */}
                {selectedScanDetails.results?.ports && selectedScanDetails.results.ports.length > 0 && (
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold text-white">🔓 Open Ports</h3>
                      <div className="flex items-center space-x-4 text-sm">
                        <span className="text-blue-400">
                          <strong>{selectedScanDetails.results.ports.length}</strong> ports found
                        </span>
                        <span className="text-green-400">
                          <strong>{selectedScanDetails.results.ports.filter((p: any) => p.service && p.service !== 'unknown').length}</strong> services identified
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {selectedScanDetails.results.ports.map((port: any, index: number) => (
                        <div key={index} className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-3">
                          <div className="text-blue-300 font-bold text-lg">{port.port}</div>
                          <div className="text-blue-400 text-sm">{port.protocol}</div>
                          <div className="text-gray-300 text-sm">{port.service || 'unknown'}</div>
                          <div className="text-gray-400 text-xs">{port.state}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Vulnérabilités */}
                {selectedScanDetails.results?.vulnerabilities && selectedScanDetails.results.vulnerabilities.length > 0 && (
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-white mb-4">🚨 Vulnerabilities ({selectedScanDetails.results.vulnerabilities.length})</h3>
                    <div className="space-y-3">
                      {selectedScanDetails.results.vulnerabilities.map((vuln: any, index: number) => (
                        <div key={index} className={`border rounded-lg p-4 ${
                          vuln.severity === 'high' ? 'bg-red-600/20 border-red-500/30' :
                          vuln.severity === 'medium' ? 'bg-yellow-600/20 border-yellow-500/30' :
                          'bg-blue-600/20 border-blue-500/30'
                        }`}>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold text-white">{vuln.name || 'Vulnerability Found'}</h4>
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              vuln.severity === 'high' ? 'bg-red-600 text-white' :
                              vuln.severity === 'medium' ? 'bg-yellow-600 text-white' :
                              'bg-blue-600 text-white'
                            }`}>
                              {vuln.severity?.toUpperCase() || 'UNKNOWN'}
                            </span>
                          </div>
                          <p className="text-gray-300 text-sm">{vuln.description}</p>
                          {vuln.cve && (
                            <p className="text-purple-400 text-sm mt-1">CVE: {vuln.cve}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* All Vulnerabilities Section for Vulnerability Scans */}
                {selectedScanDetails.results && selectedScanDetails.category === 'vulnerability' && (
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-white mb-4">🚨 All Vulnerabilities Found</h3>
                    {(() => {
                      try {
                        // Extract vulnerabilities from results
                        const vulnerabilities: any[] = [];

                        // Get vulnerabilities from results.vulnerabilities (flattened)
                        if (selectedScanDetails.results?.vulnerabilities) {
                          vulnerabilities.push(...selectedScanDetails.results.vulnerabilities);
                        }

                        // Also get vulnerabilities from individual tools if available
                        if (selectedScanDetails.results?.tools) {
                          const toolsData = selectedScanDetails.results.tools;

                          // Extract vulnerabilities from OpenVAS
                          if (toolsData.openvas?.vulnerabilities) {
                            vulnerabilities.push(...toolsData.openvas.vulnerabilities);
                          }

                          // Extract vulnerabilities from Metasploit
                          if (toolsData.metasploit?.vulnerabilities) {
                            vulnerabilities.push(...toolsData.metasploit.vulnerabilities);
                          }
                        }

                        if (vulnerabilities.length > 0) {
                            // Group vulnerabilities by severity
                            const groupedVulns = vulnerabilities.reduce((acc: any, vuln: any) => {
                              const severity = vuln.severity || 'unknown';
                              if (!acc[severity]) acc[severity] = [];
                              acc[severity].push(vuln);
                              return acc;
                            }, {});

                            const severityOrder = ['critical', 'high', 'medium', 'low', 'unknown'];

                            return (
                              <div className="space-y-4">
                                {/* Summary stats */}
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                                  {severityOrder.map(severity => {
                                    const count = groupedVulns[severity]?.length || 0;
                                    if (count === 0) return null;
                                    return (
                                      <div key={severity} className={`p-3 rounded-lg text-center ${
                                        severity === 'critical' ? 'bg-red-900/30 border border-red-500/30' :
                                        severity === 'high' ? 'bg-red-700/30 border border-red-400/30' :
                                        severity === 'medium' ? 'bg-yellow-600/30 border border-yellow-500/30' :
                                        severity === 'low' ? 'bg-blue-600/30 border border-blue-500/30' :
                                        'bg-gray-600/30 border border-gray-500/30'
                                      }`}>
                                        <div className="text-2xl font-bold text-white">{count}</div>
                                        <div className="text-sm text-gray-300 capitalize">{severity}</div>
                                      </div>
                                    );
                                  })}
                                </div>

                                {/* Detailed vulnerabilities */}
                                <div className="space-y-3">
                                  {vulnerabilities.map((vuln: any, index: number) => (
                                    <div key={index} className={`border rounded-lg p-4 ${
                                      vuln.severity === 'critical' ? 'bg-red-900/20 border-red-500/30' :
                                      vuln.severity === 'high' ? 'bg-red-600/20 border-red-500/30' :
                                      vuln.severity === 'medium' ? 'bg-yellow-600/20 border-yellow-500/30' :
                                      vuln.severity === 'low' ? 'bg-blue-600/20 border-blue-500/30' :
                                      'bg-gray-600/20 border-gray-500/30'
                                    }`}>
                                      <div className="flex items-center justify-between mb-2">
                                        <h4 className="font-semibold text-white">{vuln.name}</h4>
                                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                                          vuln.severity === 'critical' ? 'bg-red-900 text-white' :
                                          vuln.severity === 'high' ? 'bg-red-600 text-white' :
                                          vuln.severity === 'medium' ? 'bg-yellow-600 text-white' :
                                          vuln.severity === 'low' ? 'bg-blue-600 text-white' :
                                          'bg-gray-600 text-white'
                                        }`}>
                                          {vuln.severity?.toUpperCase() || 'UNKNOWN'}
                                        </span>
                                      </div>
                                      <p className="text-gray-300 text-sm mb-2">{vuln.description}</p>
                                      <div className="flex items-center justify-between text-sm">
                                        <div className="flex items-center space-x-4">
                                          {vuln.port && (
                                            <span className="text-cyan-400">Port: {vuln.port}</span>
                                          )}
                                          {vuln.source_tool && (
                                            <span className="text-purple-400">Source: {vuln.source_tool}</span>
                                          )}
                                        </div>
                                      </div>
                                      {vuln.solution && (
                                        <div className="mt-3 p-2 bg-green-600/10 border border-green-500/20 rounded">
                                          <p className="text-green-400 text-sm">
                                            <strong>Solution:</strong> {vuln.solution}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            );
                        }

                        return (
                          <div className="text-center py-8 text-gray-400">
                            <Shield className="w-12 h-12 mx-auto mb-3 opacity-50" />
                            <p>No vulnerabilities found in scan results</p>
                          </div>
                        );
                      } catch (e) {
                        console.log('Error parsing vulnerability data:', e);
                        return (
                          <div className="text-center py-8 text-gray-400">
                            <AlertTriangle className="w-12 h-12 mx-auto mb-3 opacity-50" />
                            <p>Error parsing vulnerability data</p>
                          </div>
                        );
                      }
                    })()}
                  </div>
                )}

                {/* Ports Section for Vulnerability Scans */}
                {selectedScanDetails.results && selectedScanDetails.category === 'vulnerability' && (
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-white mb-4">🔌 Ports Information</h3>
                    {(() => {
                      try {
                        // Extract port information from results
                        const ports = new Set();

                        // Get ports from results.ports if available
                        if (selectedScanDetails.results?.ports) {
                          selectedScanDetails.results.ports.forEach((port: any) => {
                            ports.add(port.port || port);
                          });
                        }

                        // Also get ports from vulnerabilities
                        if (selectedScanDetails.results?.vulnerabilities) {
                          selectedScanDetails.results.vulnerabilities.forEach((vuln: any) => {
                            if (vuln.port) ports.add(vuln.port);
                          });
                        }

                        // Get ports from individual tool results
                        if (selectedScanDetails.results?.tools) {
                          const toolsData = selectedScanDetails.results.tools;

                          if (toolsData.openvas?.vulnerabilities) {
                            toolsData.openvas.vulnerabilities.forEach((vuln: any) => {
                              if (vuln.port) ports.add(vuln.port);
                            });
                          }
                        }

                        if (ports.size > 0) {
                            return (
                              <div className="space-y-3">
                                <p className="text-gray-300 mb-4">
                                  Ports identified through vulnerability scanning:
                                </p>
                                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                                  {Array.from(ports).map((port: any, index: number) => (
                                    <div key={index} className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-3 text-center">
                                      <div className="text-blue-300 font-bold text-lg">{port}</div>
                                      <div className="text-blue-400 text-sm">TCP</div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            );
                        }

                        return (
                          <div className="text-center py-8 text-gray-400">
                            <Network className="w-12 h-12 mx-auto mb-3 opacity-50" />
                            <p>No specific port information available from vulnerability scan</p>
                          </div>
                        );
                      } catch (e) {
                        console.log('Error parsing port data:', e);
                        return (
                          <div className="text-center py-8 text-gray-400">
                            <AlertTriangle className="w-12 h-12 mx-auto mb-3 opacity-50" />
                            <p>Error parsing port data</p>
                          </div>
                        );
                      }
                    })()}
                  </div>
                )}

                {/* Raw Output - only visible to admin users */}
                {isAdmin && (
                  <div className="bg-gray-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-white mb-4">📄 Raw Output</h3>
                  <div className="space-y-4">
                    {/* Display structured output sections for vulnerability scans */}
                    {selectedScanDetails.results && selectedScanDetails.category === 'vulnerability' && (() => {
                      const sections = [];

                      // SUMMARY section
                      if (selectedScanDetails.results.summary) {
                        sections.push({
                          title: 'SUMMARY',
                          icon: '📊',
                          content: JSON.stringify(selectedScanDetails.results.summary, null, 2)
                        });
                      }

                      // TOOLS section
                      if (selectedScanDetails.results.tools) {
                        sections.push({
                          title: 'TOOLS',
                          icon: '🔧',
                          content: JSON.stringify(selectedScanDetails.results.tools, null, 2)
                        });
                      }

                      // VULNERABILITIES section
                      if (selectedScanDetails.results.vulnerabilities && selectedScanDetails.results.vulnerabilities.length > 0) {
                        sections.push({
                          title: 'VULNERABILITIES',
                          icon: '🚨',
                          content: JSON.stringify(selectedScanDetails.results.vulnerabilities, null, 2)
                        });
                      }

                      // PORTS section
                      if (selectedScanDetails.results.ports && selectedScanDetails.results.ports.length > 0) {
                        sections.push({
                          title: 'PORTS',
                          icon: '🔌',
                          content: JSON.stringify(selectedScanDetails.results.ports, null, 2)
                        });
                      }

                      return sections.map((section, index) => (
                        <div key={index} className="bg-gray-900/50 rounded-lg p-4">
                          <h4 className="text-lg font-medium text-white mb-2 flex items-center space-x-2">
                            <span>{section.icon}</span>
                            <span>{section.title} Output</span>
                          </h4>
                          <pre className="text-gray-300 text-sm overflow-x-auto whitespace-pre-wrap bg-black/30 p-3 rounded">
                            {section.content}
                          </pre>
                        </div>
                      ));
                    })()}

                    {/* Display regular results for other scan types */}
                    {selectedScanDetails.results && Object.keys(selectedScanDetails.results).map((tool) => {
                      if (tool === 'ports' || tool === 'vulnerabilities') return null;
                      return (
                        <div key={tool} className="bg-gray-900/50 rounded-lg p-4">
                          <h4 className="text-lg font-medium text-white mb-2 flex items-center space-x-2">
                            <span>{getToolIcon(tool)}</span>
                            <span>{tool.toUpperCase()} Output</span>
                          </h4>
                          <pre className="text-gray-300 text-sm overflow-x-auto whitespace-pre-wrap bg-black/30 p-3 rounded">
                            {typeof selectedScanDetails.results[tool] === 'string'
                              ? selectedScanDetails.results[tool]
                              : JSON.stringify(selectedScanDetails.results[tool], null, 2)}
                          </pre>
                        </div>
                      );
                    })}
                  </div>
                  </div>
                )}
              </div>

              {/* Modal Actions */}
              <div className="border-t border-gray-700/50 p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {selectedScanDetails.status !== 'running' && (
                      <>
                        {selectedScanDetails.status === 'stopped' && (
                          <button
                            onClick={() => {
                              handleRestartScan(selectedScanDetails.scan_id);
                              setShowScanDetails(false);
                            }}
                            className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                          >
                            <RefreshCw className="w-4 h-4" />
                            <span>Resume Scan</span>
                          </button>
                        )}
                        {/* Delete button - only visible to admin users */}
                        {isAdmin && (
                          <button
                            onClick={() => {
                              handleDeleteScan(selectedScanDetails.scan_id);
                              setShowScanDetails(false);
                            }}
                            className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                          >
                            <X className="w-4 h-4" />
                            <span>Delete Scan</span>
                          </button>
                        )}
                      </>
                    )}
                  </div>
                  <button
                    onClick={() => setShowScanDetails(false)}
                    className="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}
