import { useState, useEffect } from 'react';
import {
  Trash2,
  Refresh<PERSON><PERSON>,
  <PERSON>,
  Settings,
  <PERSON>ert<PERSON>riangle,
  CheckCircle,
  Clock,
  Shield,
  Database,
  Zap
} from 'lucide-react';
import Cookies from 'js-cookie';
import { useAuth } from '../hooks/useAuth';
import { useModalContext } from '../contexts/ModalContext';

interface OpenVASTarget {
  id: string;
  name: string;
  hosts: string;
  comment?: string;
  creation_time?: string;
}

interface OpenVASTask {
  id: string;
  name: string;
  status: string;
  target?: string;
  creation_time?: string;
  last_report?: string;
}

export default function OpenVASAdmin() {
  const { user } = useAuth();
  const { showAlert, showConfirm } = useModalContext();
  const [targets, setTargets] = useState<OpenVASTarget[]>([]);
  const [tasks, setTasks] = useState<OpenVASTask[]>([]);
  const [loadingTargets, setLoadingTargets] = useState(true);
  const [loadingTasks, setLoadingTasks] = useState(true);
  const [deleting, setDeleting] = useState<string | null>(null);
  const [cleaning, setCleaning] = useState(false);

  // Rediriger si pas admin
  useEffect(() => {
    if (user && user.role !== 'admin') {
      window.location.href = '/pentesting';
    }
  }, [user]);

  useEffect(() => {
    loadTargets();
    loadTasks();
  }, []);

  const loadTargets = async () => {
    try {
      setLoadingTargets(true);
      const token = Cookies.get('token');

      const response = await fetch('http://localhost:5000/scan/pentesting/admin/openvas/targets', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTargets(data.targets || []);
      } else {
        console.error('Failed to load targets:', response.status);
      }
    } catch (error) {
      console.error('Error loading targets:', error);
    } finally {
      setLoadingTargets(false);
    }
  };

  const loadTasks = async () => {
    try {
      setLoadingTasks(true);
      const token = Cookies.get('token');

      const response = await fetch('http://localhost:5000/scan/pentesting/admin/openvas/tasks', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks || []);
      } else {
        console.error('Failed to load tasks:', response.status);
      }
    } catch (error) {
      console.error('Error loading tasks:', error);
    } finally {
      setLoadingTasks(false);
    }
  };

  const deleteTarget = async (targetId: string) => {
    const confirmed = await showConfirm({
      type: 'danger',
      title: 'Supprimer la cible',
      message: 'Êtes-vous sûr de vouloir supprimer cette cible ?',
      confirmText: 'Supprimer',
      cancelText: 'Annuler'
    });

    if (!confirmed) return;
    
    try {
      setDeleting(targetId);
      const token = Cookies.get('token');
      
      const response = await fetch(`http://localhost:5000/scan/pentesting/admin/openvas/targets/${targetId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setTargets(prev => prev.filter(t => t.id !== targetId));
        showAlert({
          type: 'success',
          title: 'Success',
          message: 'Target deleted successfully'
        });
      } else {
        const error = await response.json();
        showAlert({
          type: 'error',
          title: 'Erreur',
          message: `Échec de la suppression de la cible: ${error.error}`
        });
      }
    } catch (error) {
      console.error('Error deleting target:', error);
      showAlert({
        type: 'error',
        title: 'Erreur',
        message: 'Erreur lors de la suppression de la cible'
      });
    } finally {
      setDeleting(null);
    }
  };

  const deleteTask = async (taskId: string) => {
    const confirmed = await showConfirm({
      type: 'danger',
      title: 'Supprimer la tâche',
      message: 'Êtes-vous sûr de vouloir supprimer cette tâche ?',
      confirmText: 'Supprimer',
      cancelText: 'Annuler'
    });

    if (!confirmed) return;
    
    try {
      setDeleting(taskId);
      const token = Cookies.get('token');
      
      const response = await fetch(`http://localhost:5000/scan/pentesting/admin/openvas/tasks/${taskId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setTasks(prev => prev.filter(t => t.id !== taskId));
        showAlert({
          type: 'success',
          title: 'Success',
          message: 'Task deleted successfully'
        });
      } else {
        const error = await response.json();
        showAlert({
          type: 'error',
          title: 'Erreur',
          message: `Échec de la suppression de la tâche: ${error.error}`
        });
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      showAlert({
        type: 'error',
        title: 'Erreur',
        message: 'Erreur lors de la suppression de la tâche'
      });
    } finally {
      setDeleting(null);
    }
  };

  const cleanupAll = async () => {
    const confirmed = await showConfirm({
      type: 'danger',
      title: 'Nettoyage complet',
      message: '⚠️ Cela supprimera TOUTES les cibles et tâches OpenVAS. Êtes-vous sûr ?',
      confirmText: 'Tout supprimer',
      cancelText: 'Annuler'
    });

    if (!confirmed) return;
    
    try {
      setCleaning(true);
      const token = Cookies.get('token');
      
      const response = await fetch('http://localhost:5000/scan/pentesting/admin/openvas/cleanup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        showAlert({
          type: 'success',
          title: 'Nettoyage terminé',
          message: `Nettoyage terminé !\nSupprimé: ${result.deleted_tasks} tâches, ${result.deleted_targets} cibles`
        });
        setTargets([]);
        setTasks([]);
      } else {
        const error = await response.json();
        showAlert({
          type: 'error',
          title: 'Échec du nettoyage',
          message: `Échec du nettoyage: ${error.error}`
        });
      }
    } catch (error) {
      console.error('Error during cleanup:', error);
      showAlert({
        type: 'error',
        title: 'Erreur',
        message: 'Erreur lors du nettoyage'
      });
    } finally {
      setCleaning(false);
    }
  };

  const getTaskStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'done':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'running':
        return <Clock className="w-4 h-4 text-yellow-400 animate-spin" />;
      case 'stopped':
      case 'interrupted':
        return <AlertTriangle className="w-4 h-4 text-red-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTaskStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'done':
        return 'text-green-400';
      case 'running':
        return 'text-yellow-400';
      case 'stopped':
      case 'interrupted':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  if (user?.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Shield className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-gray-400">This page is only accessible to administrators.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-lg flex items-center justify-center shadow-lg">
              <Settings className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">OpenVAS Administration</h1>
              <p className="text-gray-400">Manage OpenVAS targets and tasks</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={() => { loadTargets(); loadTasks(); }}
              disabled={loadingTargets || loadingTasks}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${(loadingTargets || loadingTasks) ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
            
            <button
              onClick={cleanupAll}
              disabled={cleaning}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors"
            >
              <Zap className={`w-4 h-4 ${cleaning ? 'animate-spin' : ''}`} />
              <span>Cleanup All</span>
            </button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                <Target className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white">Targets</h3>
                <p className="text-3xl font-bold text-blue-400">{targets.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Database className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white">Tasks</h3>
                <p className="text-3xl font-bold text-purple-400">{tasks.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Targets Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
              <Target className="w-6 h-6 text-blue-400" />
              <span>OpenVAS Targets</span>
            </h2>
          </div>

          {loadingTargets ? (
            <div className="text-center py-12">
              <RefreshCw className="w-8 h-8 text-blue-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-400">Loading targets...</p>
            </div>
          ) : targets.length === 0 ? (
            <div className="text-center py-12">
              <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">No Targets Found</h3>
              <p className="text-gray-400">No OpenVAS targets are currently configured</p>
            </div>
          ) : (
            <div className="space-y-4">
              {targets.map((target) => (
                <div
                  key={target.id}
                  className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6 hover:bg-gray-700/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                        <Target className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white">{target.name}</h3>
                        <p className="text-gray-300">{target.hosts}</p>
                        {target.comment && (
                          <p className="text-gray-400 text-sm">{target.comment}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-gray-400 text-sm">ID: {target.id}</p>
                        {target.creation_time && (
                          <p className="text-gray-400 text-sm">
                            Created: {new Date(target.creation_time).toLocaleString()}
                          </p>
                        )}
                      </div>
                      <button
                        onClick={() => deleteTarget(target.id)}
                        disabled={deleting === target.id}
                        className="flex items-center space-x-1 px-3 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span>Delete</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Tasks Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
              <Database className="w-6 h-6 text-purple-400" />
              <span>OpenVAS Tasks</span>
            </h2>
          </div>

          {loadingTasks ? (
            <div className="text-center py-12">
              <RefreshCw className="w-8 h-8 text-purple-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-400">Loading tasks...</p>
            </div>
          ) : tasks.length === 0 ? (
            <div className="text-center py-12">
              <Database className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">No Tasks Found</h3>
              <p className="text-gray-400">No OpenVAS tasks are currently configured</p>
            </div>
          ) : (
            <div className="space-y-4">
              {tasks.map((task) => (
                <div
                  key={task.id}
                  className="bg-gray-800/50 border border-gray-600/30 rounded-xl p-6 hover:bg-gray-700/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <Database className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white">{task.name}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          {getTaskStatusIcon(task.status)}
                          <span className={`font-medium uppercase ${getTaskStatusColor(task.status)}`}>
                            {task.status}
                          </span>
                        </div>
                        {task.target && (
                          <p className="text-gray-400 text-sm">Target: {task.target}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-gray-400 text-sm">ID: {task.id}</p>
                        {task.creation_time && (
                          <p className="text-gray-400 text-sm">
                            Created: {new Date(task.creation_time).toLocaleString()}
                          </p>
                        )}
                        {task.last_report && (
                          <p className="text-gray-400 text-sm">
                            Last Report: {new Date(task.last_report).toLocaleString()}
                          </p>
                        )}
                      </div>
                      <button
                        onClick={() => deleteTask(task.id)}
                        disabled={deleting === task.id}
                        className="flex items-center space-x-1 px-3 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span>Delete</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
