import { useState, useEffect } from 'react';
import {
  <PERSON>r,
  <PERSON>,
  <PERSON>,
  Users,
  Zap,
  Key,
  Camera,
  Save,
  Moon,
  Sun,
  Mail,
  MessageSquare
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import { getToken } from '../utils/auth';

interface NotificationSettings {
  email_enabled: boolean;
  email_address: string;
  telegram_enabled: boolean;
  telegram_chat_id: string;
}

export default function Settings() {
  const { user } = useAuth();
  const { isAdmin } = useRole();
  const [activeTab, setActiveTab] = useState('profile');
  const [darkMode, setDarkMode] = useState(true);
  const [timezone, setTimezone] = useState('UTC');
  const [dateFormat, setDateFormat] = useState('MMDD, YYYY (Jun 15, 2023)');

  // Form states
  const [fullName, setFullName] = useState(user?.username || '<PERSON>');
  const [email, setEmail] = useState(user?.email || '<EMAIL>');
  const [role, setRole] = useState(user?.role || 'Administrator');

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    email_enabled: false,
    email_address: '',
    telegram_enabled: false,
    telegram_chat_id: ''
  });
  const [notificationLoading, setNotificationLoading] = useState(false);
  const [notificationSuccess, setNotificationSuccess] = useState<string | null>(null);
  const [notificationError, setNotificationError] = useState<string | null>(null);
 

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'team', label: 'Team', icon: Users },
    { id: 'integrations', label: 'Integrations', icon: Zap },
    { id: 'api', label: 'API Access', icon: Key },
  ];

  useEffect(() => {
    if (isAdmin && activeTab === 'notifications') {
      loadNotificationSettings();
    }
  }, [isAdmin, activeTab]);

  const loadNotificationSettings = async () => {
    try {
      const token = getToken();
      if (!token) {
        console.error('No authentication token found');
        return;
      }

      const response = await fetch('http://localhost:5000/api/incident/notification-settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const settings = await response.json();
        setNotificationSettings({
          email_enabled: settings.email_enabled || false,
          email_address: settings.email_address || '',
          telegram_enabled: settings.telegram_enabled || false,
          telegram_chat_id: settings.telegram_chat_id || ''
        });
      }
    } catch (err) {
      console.error('Failed to load notification settings:', err);
    }
  };

  const saveNotificationSettings = async () => {
    setNotificationLoading(true);
    setNotificationError(null);
    setNotificationSuccess(null);

    try {
      const token = getToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:5000/api/incident/notification-settings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(notificationSettings)
      });

      if (response.ok) {
        setNotificationSuccess('Notification settings saved successfully!');
        setTimeout(() => setNotificationSuccess(null), 3000);
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (err) {
      setNotificationError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setNotificationLoading(false);
    }
  };



  const handleSaveChanges = () => {
    // TODO: Implement save functionality
    console.log('Saving changes...');
  };

  const renderProfileTab = () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">Profile Settings</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Avatar Section */}
          <div className="lg:col-span-1">
            <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
              <div className="text-center">
                <div className="relative inline-block">
                  <div className="w-32 h-32 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-full flex items-center justify-center text-white text-4xl font-bold shadow-2xl">
                    JD
                  </div>
                  <button className="absolute bottom-2 right-2 bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-full transition-colors shadow-lg">
                    <Camera className="w-4 h-4" />
                  </button>
                </div>
                <button className="mt-4 text-purple-400 hover:text-purple-300 text-sm transition-colors">
                  Change Avatar
                </button>
              </div>
            </div>
          </div>

          {/* Profile Form */}
          <div className="lg:col-span-2">
            <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                <input
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Role</label>
                <select
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                >
                  <option value="Administrator">Administrator</option>
                  <option value="User">User</option>
                  <option value="Moderator">Moderator</option>
                </select>
                <p className="text-xs text-gray-400 mt-1">
                  Note: role can be changed by a Workspace administrator.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Interface Preferences */}
      <div>
        <h3 className="text-xl font-semibold text-white mb-4">Interface Preferences</h3>
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 space-y-6">
          {/* Dark Mode */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {darkMode ? <Moon className="w-5 h-5 text-purple-400" /> : <Sun className="w-5 h-5 text-yellow-400" />}
              <div>
                <label className="text-white font-medium">Dark Mode</label>
                <p className="text-sm text-gray-400">Toggle dark/light theme</p>
              </div>
            </div>
            <button
              onClick={() => setDarkMode(!darkMode)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                darkMode ? 'bg-purple-600' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  darkMode ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {/* Timezone */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Timezone</label>
            <select
              value={timezone}
              onChange={(e) => setTimezone(e.target.value)}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            >
              <option value="UTC">UTC</option>
              <option value="EST">EST</option>
              <option value="PST">PST</option>
              <option value="CET">CET</option>
            </select>
          </div>

          {/* Date Format */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Date Format</label>
            <select
              value={dateFormat}
              onChange={(e) => setDateFormat(e.target.value)}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
            >
              <option value="MMDD, YYYY (Jun 15, 2023)">MMDD, YYYY (Jun 15, 2023)</option>
              <option value="DD/MM/YYYY (15/06/2023)">DD/MM/YYYY (15/06/2023)</option>
              <option value="YYYY-MM-DD (2023-06-15)">YYYY-MM-DD (2023-06-15)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSaveChanges}
          className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
        >
          <Save className="w-4 h-4" />
          <span>Save Changes</span>
        </button>
      </div>
    </div>
  );

  const renderNotificationsTab = () => {
    if (!isAdmin) {
      return (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
            <Bell className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Admin Access Required</h3>
          <p className="text-gray-400">
            Notification settings are only available for administrators.
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-white mb-6">Notification Settings</h2>

        {/* Success/Error Messages */}
        {notificationSuccess && (
          <div className="bg-green-900/50 border border-green-500 rounded-xl p-4">
            <p className="text-green-200">{notificationSuccess}</p>
          </div>
        )}

        {notificationError && (
          <div className="bg-red-900/50 border border-red-500 rounded-xl p-4">
            <p className="text-red-200">{notificationError}</p>
          </div>
        )}

        {/* Email Notifications */}
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Mail size={20} className="text-blue-400" />
              <div>
                <h3 className="text-white font-medium">Email Notifications</h3>
                <p className="text-sm text-gray-400">Receive incident notifications via email</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notificationSettings.email_enabled}
                onChange={(e) => setNotificationSettings(prev => ({
                  ...prev,
                  email_enabled: e.target.checked
                }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          {notificationSettings.email_enabled && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email Address
              </label>
              <input
                type="email"
                value={notificationSettings.email_address}
                onChange={(e) => setNotificationSettings(prev => ({
                  ...prev,
                  email_address: e.target.value
                }))}
                placeholder="<EMAIL>"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          )}
        </div>

        {/* Telegram Notifications */}
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <MessageSquare size={20} className="text-cyan-400" />
              <div>
                <h3 className="text-white font-medium">Telegram Notifications</h3>
                <p className="text-sm text-gray-400">Receive incident notifications via Telegram</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notificationSettings.telegram_enabled}
                onChange={(e) => setNotificationSettings(prev => ({
                  ...prev,
                  telegram_enabled: e.target.checked
                }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          {notificationSettings.telegram_enabled && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Telegram Chat ID
              </label>
              <input
                type="text"
                value={notificationSettings.telegram_chat_id}
                onChange={(e) => setNotificationSettings(prev => ({
                  ...prev,
                  telegram_chat_id: e.target.value
                }))}
                placeholder="5694506830"
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
              />
              <p className="text-xs text-gray-500 mt-2">
                Get your Chat ID by messaging @userinfobot on Telegram
              </p>
            </div>
          )}
        </div>

        {/* WebSocket Status */}
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <div>
              <h3 className="text-white font-medium">Real-time Notifications</h3>
              <p className="text-sm text-gray-400">WebSocket notifications are always enabled for admins</p>
            </div>
          </div>
        </div>

        {/* Configuration Info */}
        <div className="bg-blue-900/30 border border-blue-500/50 rounded-2xl p-6">
          <div className="flex items-start space-x-3">
            <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
              <span className="text-white text-xs font-bold">i</span>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">Configuration Information</h3>
              <div className="text-sm text-gray-300 space-y-1">
                <p>• <strong>Telegram Bot:</strong> Configured with token ending in ...mdDA</p>
                <p>• <strong>Default Chat ID:</strong> 5694506830 (can be overridden per admin)</p>
                <p>• <strong>Supported Events:</strong> Ticket creation, updates, assignments, and conversions</p>
                <p>• <strong>Message Format:</strong> HTML with emojis and structured information</p>
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            onClick={saveNotificationSettings}
            disabled={notificationLoading}
            className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:transform-none"
          >
            {notificationLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>Save Notification Settings</span>
          </button>
        </div>
      </div>
    );
  };

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white mb-6">Security Settings</h2>

      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 space-y-6">
        <div>
          <h3 className="text-white font-medium mb-4">Two-Factor Authentication</h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Add an extra layer of security to your account</p>
            </div>
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
              Enable 2FA
            </button>
          </div>
        </div>

        <div>
          <h3 className="text-white font-medium mb-4">Change Password</h3>
          <div className="space-y-4">
            <input
              type="password"
              placeholder="Current password"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <input
              type="password"
              placeholder="New password"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <input
              type="password"
              placeholder="Confirm new password"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <button className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
              Update Password
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderOtherTabs = (tabId: string) => (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
        <Shield className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-xl font-semibold text-white mb-2">
        {tabs.find(tab => tab.id === tabId)?.label} Settings
      </h3>
      <p className="text-gray-400">
        Cette section sera implémentée prochainement.
      </p>
    </div>
  );

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <div className="w-64 bg-gray-900/60 backdrop-blur-xl border-r border-gray-700/50 p-4">
        <h1 className="text-xl font-bold text-white mb-6">Settings</h1>
        <nav className="space-y-2">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        {activeTab === 'profile' && renderProfileTab()}
        {activeTab === 'notifications' && renderNotificationsTab()}
        {activeTab === 'security' && renderSecurityTab()}
        {!['profile', 'notifications', 'security'].includes(activeTab) && renderOtherTabs(activeTab)}
      </div>
    </div>
  );
}
