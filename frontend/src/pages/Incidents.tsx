import { useState, useEffect } from 'react';
import {
  AlertTriangle,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  UserPlus,
  ArrowRight,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  FileDown
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useRole } from '../hooks/useRole';
import { useModalContext } from '../contexts/ModalContext';
import Cookies from 'js-cookie';
import {
  InvestigationTicket,
  Incident,
  getTickets,
  getIncidents,
  getSeverityColor,
  getStatusColor,
  getPriorityColor,
  getPriorityLabel,
  formatDate
} from '../services/incidentService';
import CreateTicketForm from '../components/incidents/CreateTicketForm';
import TicketDetailsModal from '../components/incidents/TicketDetailsModal';
import IncidentDetailsModal from '../components/incidents/IncidentDetailsModal';

export default function Incidents() {
  const { user } = useAuth();
  const { isAdmin } = useRole();
  const { showAlert } = useModalContext();
  const [activeTab, setActiveTab] = useState<'tickets' | 'incidents'>('tickets');
  const [tickets, setTickets] = useState<InvestigationTicket[]>([]);
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<InvestigationTicket | null>(null);
  const [showTicketDetails, setShowTicketDetails] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null);
  const [showIncidentDetails, setShowIncidentDetails] = useState(false);
  const [ticketModalMode, setTicketModalMode] = useState<'view' | 'edit' | 'assign' | 'convert'>('view');
  const [exportingCSV, setExportingCSV] = useState(false);

  // Load data on component mount
  useEffect(() => {
    loadTickets();
    loadIncidents();
  }, [user]);

  const loadTickets = async () => {
    try {
      setLoading(true);
      const data = await getTickets();
      setTickets(data);
    } catch (error) {
      console.error('Failed to load tickets:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadIncidents = async () => {
    try {
      const data = await getIncidents();
      setIncidents(data);
    } catch (error) {
      console.error('Failed to load incidents:', error);
    }
  };



  const handleViewTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('view');
    setShowTicketDetails(true);
  };

  const handleEditTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('edit');
    setShowTicketDetails(true);
  };

  const handleAssignTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('assign');
    setShowTicketDetails(true);
  };

  const handleConvertTicket = (ticket: InvestigationTicket) => {
    setSelectedTicket(ticket);
    setTicketModalMode('convert');
    setShowTicketDetails(true);
  };

  const handleTicketUpdated = () => {
    loadTickets();
    loadIncidents();
  };

  const handleViewIncident = (incident: Incident) => {
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
  };

  const handleEditIncident = (incident: Incident) => {
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
    // The modal will handle edit mode internally
  };

  const handleAssignIncident = (incident: Incident) => {
    // Open the incident details modal in edit mode for assignment
    setSelectedIncident(incident);
    setShowIncidentDetails(true);
  };

  const handleIncidentUpdated = () => {
    loadIncidents();
    setSelectedIncident(null);
    setShowIncidentDetails(false);
  };

  // Function to export all incidents and tickets to CSV
  const handleExportIncidentsCSV = async () => {
    try {
      setExportingCSV(true);
      console.log('📊 Exporting all incidents and tickets to CSV');

      const token = Cookies.get('token');
      if (!token) {
        showAlert({
          type: 'error',
          title: 'Authentication required',
          message: 'Please log in to export data'
        });
        return;
      }

      const response = await fetch(`http://localhost:5000/api/export/incidents/csv`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Generate filename
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        link.download = `PICA_Incidents_Export_${timestamp}.csv`;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('✅ Incidents CSV exported successfully');

        showAlert({
          type: 'success',
          title: 'Export successful',
          message: 'All incidents and tickets exported successfully to CSV.'
        });
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to export incidents CSV:', response.status, errorData);
        showAlert({
          type: 'error',
          title: 'Export error',
          message: 'CSV export error: ' + (errorData.message || 'Unknown error')
        });
      }
    } catch (error) {
      console.error('❌ Error exporting incidents CSV:', error);
      showAlert({
        type: 'error',
        title: 'Export error',
        message: 'An error occurred during CSV export'
      });
    } finally {
      setExportingCSV(false);
    }
  };

  // Filter tickets based on search and filters
  const filteredTickets = tickets.filter(ticket => {
    const title = ticket.short_description || ticket.title || '';
    const matchesSearch = title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.ticket_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (ticket.category && ticket.category.toLowerCase().includes(searchTerm.toLowerCase()));

    // Handle both old severity and new impact/priority filtering
    const ticketSeverity = ticket.severity || ticket.impact?.toLowerCase();
    const matchesSeverity = severityFilter === 'all' || ticketSeverity === severityFilter;
    const matchesStatus = statusFilter === 'all' || ticket.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  // Filter incidents based on search and filters
  const filteredIncidents = incidents.filter(incident => {
    const matchesSearch = incident.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incident.incident_id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSeverity = severityFilter === 'all' || incident.severity === severityFilter;
    const matchesStatus = statusFilter === 'all' || incident.status === statusFilter;
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  const renderTabButton = (tab: 'tickets' | 'incidents' | 'settings', label: string, icon: any) => {
    const Icon = icon;
    return (
      <button
        onClick={() => setActiveTab(tab)}
        className={`flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
          activeTab === tab
            ? 'bg-purple-600 text-white shadow-lg shadow-purple-600/30'
            : 'text-gray-400 hover:text-white hover:bg-gray-800/80'
        }`}
      >
        <Icon size={20} className="mr-2" />
        {label}
      </button>
    );
  };

  const renderFilters = () => (
    <div className="flex flex-wrap gap-4 mb-6">
      <div className="flex-1 min-w-64">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search tickets, incidents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>
      </div>
      
      <select
        value={severityFilter}
        onChange={(e) => setSeverityFilter(e.target.value)}
        className="px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
      >
        <option value="all">All Severities</option>
        <option value="critical">Critical</option>
        <option value="high">High</option>
        <option value="medium">Medium</option>
        <option value="low">Low</option>
      </select>

      <select
        value={statusFilter}
        onChange={(e) => setStatusFilter(e.target.value)}
        className="px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
      >
        <option value="all">All Statuses</option>
        <option value="open">Open</option>
        <option value="in_progress">In Progress</option>
        <option value="converted_to_incident">Converted</option>
        <option value="resolved">Resolved</option>
        <option value="closed">Closed</option>
      </select>

      <button
        onClick={() => {
          loadTickets();
          loadIncidents();
        }}
        className="flex items-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
      >
        <RefreshCw size={20} className="mr-2" />
        Refresh
      </button>
    </div>
  );

  const renderTicketCard = (ticket: InvestigationTicket) => {
    const title = ticket.short_description || ticket.title || 'No title';
    const severity = ticket.severity || ticket.impact?.toLowerCase();

    return (
      <div key={ticket.id} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:border-purple-500/50 transition-all duration-200">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold text-white">{title}</h3>
              {ticket.category && (
                <span className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
                  {ticket.category}
                </span>
              )}
            </div>
            <p className="text-gray-400 text-sm mb-2">{ticket.ticket_number}</p>
            <p className="text-gray-300 text-sm line-clamp-2">{ticket.description}</p>

            {/* ITSM Fields */}
            <div className="flex items-center gap-4 mt-3 text-xs text-gray-400">
              {ticket.impact && ticket.urgency && (
                <span>Impact: {ticket.impact} | Urgency: {ticket.urgency}</span>
              )}
              {ticket.assignment_group && (
                <span>Group: {ticket.assignment_group}</span>
              )}
              {ticket.configuration_item && (
                <span>CI: {ticket.configuration_item}</span>
              )}
            </div>
          </div>

          <div className="flex flex-col items-end space-y-2 ml-4">
            {ticket.priority && (
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                {getPriorityLabel(ticket.priority)}
              </span>
            )}
            {severity && (
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(severity)}`}>
                {severity.toUpperCase()}
              </span>
            )}
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
              {ticket.status.replace('_', ' ').toUpperCase()}
            </span>
          </div>
        </div>
      
      <div className="flex items-center justify-between text-sm text-gray-400">
        <div className="flex items-center space-x-4">
          <span className="flex items-center">
            <Clock size={16} className="mr-1" />
            {formatDate(ticket.created_at)}
          </span>
          {ticket.assigned_to && (
            <span className="flex items-center">
              <UserPlus size={16} className="mr-1" />
              Assigned to: {ticket.assigned_to}
            </span>
          )}
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => handleViewTicket(ticket)}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>

          {/* Edit button - Available for ticket owner or admin */}
          {(isAdmin || ticket.user_id === user?.id) && (
            <button
              onClick={() => handleEditTicket(ticket)}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              title="Edit Ticket"
            >
              <Edit size={16} />
            </button>
          )}

          {/* Admin-only actions */}
          {isAdmin && (
            <>
              <button
                onClick={() => handleAssignTicket(ticket)}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
                title="Assign Ticket"
              >
                <UserPlus size={16} />
              </button>
              {ticket.status !== 'converted_to_incident' && (
                <button
                  onClick={() => handleConvertTicket(ticket)}
                  className="p-2 text-purple-400 hover:text-purple-300 hover:bg-purple-900/30 rounded-lg transition-colors"
                  title="Convert to Incident"
                >
                  <ArrowRight size={16} />
                </button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
    );
  };

  const renderIncidentCard = (incident: Incident) => (
    <div key={incident.id} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:border-purple-500/50 transition-all duration-200">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-white mb-2">{incident.title}</h3>
          <p className="text-gray-400 text-sm mb-2">{incident.incident_id}</p>
          <p className="text-gray-300 text-sm">{incident.description}</p>
        </div>
        <div className="flex flex-col items-end space-y-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getSeverityColor(incident.severity)}`}>
            {incident.severity.toUpperCase()}
          </span>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(incident.status)}`}>
            {incident.status.replace('_', ' ').toUpperCase()}
          </span>
        </div>
      </div>
      
      <div className="flex items-center justify-between text-sm text-gray-400">
        <div className="flex items-center space-x-4">
          <span className="flex items-center">
            <Clock size={16} className="mr-1" />
            {formatDate(incident.created_at)}
          </span>
          {incident.assigned_to && (
            <span className="flex items-center text-green-400">
              <UserPlus size={16} className="mr-1" />
              Assigned to: {incident.assigned_to}
            </span>
          )}
          {incident.escalated && (
            <span className="flex items-center text-red-400">
              <AlertTriangle size={16} className="mr-1" />
              Escalated
            </span>
          )}
          {incident.false_positive && (
            <span className="flex items-center text-yellow-400">
              <XCircle size={16} className="mr-1" />
              False Positive
            </span>
          )}
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => handleViewIncident(incident)}
            className="p-2 text-gray-400 hover:text-blue-400 hover:bg-blue-900/30 rounded-lg transition-colors"
            title="View Incident Details"
          >
            <Eye size={16} />
          </button>
          {isAdmin && (
            <>
              <button
                onClick={() => handleEditIncident(incident)}
                className="p-2 text-gray-400 hover:text-purple-400 hover:bg-purple-900/30 rounded-lg transition-colors"
                title="Edit Incident"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => handleAssignIncident(incident)}
                className="p-2 text-gray-400 hover:text-green-400 hover:bg-green-900/30 rounded-lg transition-colors"
                title="Assign Incident"
              >
                <UserPlus size={16} />
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Incident Management</h1>
            <p className="text-gray-400">Manage security incidents and investigation tickets</p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Export CSV Button - Always visible for authenticated users */}
            <button
              onClick={handleExportIncidentsCSV}
              disabled={exportingCSV}
              className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
              title="Export all incidents and tickets to CSV"
            >
              {exportingCSV ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Exporting...</span>
                </>
              ) : (
                <>
                  <FileDown className="w-4 h-4" />
                  <span>Export All CSV</span>
                </>
              )}
            </button>

            <button
              onClick={() => setShowCreateForm(true)}
              className="flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors shadow-lg shadow-purple-600/30"
            >
              <Plus size={20} className="mr-2" />
              Create Ticket
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-4 mb-8">
          {renderTabButton('tickets', 'Investigation Tickets', AlertTriangle)}
          {renderTabButton('incidents', 'Incidents', CheckCircle)}
        </div>

        {/* Content */}
        {(activeTab === 'tickets' || activeTab === 'incidents') && renderFilters()}

        {activeTab === 'tickets' && (
          <div className="space-y-4">
            {loading ? (
              <div className="text-center py-12">
                <RefreshCw className="animate-spin mx-auto mb-4 text-purple-400" size={48} />
                <p className="text-gray-400">Loading tickets...</p>
              </div>
            ) : filteredTickets.length > 0 ? (
              filteredTickets.map(renderTicketCard)
            ) : (
              <div className="text-center py-12">
                <AlertTriangle className="mx-auto mb-4 text-gray-400" size={48} />
                <p className="text-gray-400 text-lg mb-2">
                  {isAdmin ? 'No tickets found' : 'You haven\'t created any tickets yet'}
                </p>
                <p className="text-gray-500 text-sm">
                  {isAdmin
                    ? 'Tickets will appear here when users submit them'
                    : 'Create your first ticket to report an issue or request assistance'
                  }
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'incidents' && (
          <div className="space-y-4">
            {filteredIncidents.length > 0 ? (
              filteredIncidents.map(renderIncidentCard)
            ) : (
              <div className="text-center py-12">
                <CheckCircle className="mx-auto mb-4 text-gray-400" size={48} />
                <p className="text-gray-400">No incidents found</p>
              </div>
            )}
          </div>
        )}



        {/* Create Ticket Form Modal */}
        <CreateTicketForm
          isOpen={showCreateForm}
          onClose={() => setShowCreateForm(false)}
          onTicketCreated={() => {
            loadTickets();
            setShowCreateForm(false);
          }}
        />

        {/* Ticket Details Modal */}
        <TicketDetailsModal
          ticket={selectedTicket}
          isOpen={showTicketDetails}
          onClose={() => {
            setShowTicketDetails(false);
            setSelectedTicket(null);
            setTicketModalMode('view');
          }}
          onTicketUpdated={handleTicketUpdated}
          initialMode={ticketModalMode}
          currentUserId={user?.id}
        />

        {/* Incident Details Modal */}
        <IncidentDetailsModal
          incident={selectedIncident}
          isOpen={showIncidentDetails}
          onClose={() => {
            setShowIncidentDetails(false);
            setSelectedIncident(null);
          }}
          onIncidentUpdated={handleIncidentUpdated}
        />
      </div>
    </div>
  );
}
