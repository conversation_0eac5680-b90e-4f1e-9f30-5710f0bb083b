.running-scans-monitor {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.auto-refresh {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.9rem;
}

.auto-refresh input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.5);
  color: #fecaca;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.scans-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: calc(100vh - 140px);
}

.scans-list {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  overflow-y: auto;
}

.scans-list h3 {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.no-scans {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.scan-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scan-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.scan-item.selected {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.scan-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.category-icon {
  font-size: 1.5rem;
}

.scan-info {
  flex: 1;
}

.scan-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 2px;
}

.scan-subtitle {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
}

.scan-status {
  text-align: right;
  font-size: 0.8rem;
}

.current-tool {
  margin-bottom: 2px;
  color: #fbbf24;
}

.duration {
  color: rgba(255, 255, 255, 0.8);
}

.progress-bar {
  position: relative;
  background: rgba(255, 255, 255, 0.2);
  height: 6px;
  border-radius: 3px;
  margin: 8px 0;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, #10b981, #3b82f6);
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -20px;
  right: 0;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
}

.tools-status {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.deep-scan-mini-summary {
  display: flex;
  gap: 8px;
  margin-top: 6px;
  font-size: 0.75rem;
}

.mini-stat {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.tool-status {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.scan-details {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  overflow-y: auto;
}

.details-content h3 {
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 25px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.detail-item span {
  font-size: 0.9rem;
  color: white;
}

.tools-detailed-status h4 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
  margin-top: 15px;
}

.tool-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.tool-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.tool-detailed-status {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.tool-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.tool-status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tool-progress {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 6px;
}

.progress-bar-small {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill-small {
  background: linear-gradient(90deg, #10b981, #3b82f6);
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-percent {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  min-width: 35px;
}

.tool-timing {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.tool-error {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fecaca;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  margin-top: 6px;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* Deep scan summary styles */
.deep-scan-summary {
  margin-top: 20px;
  padding: 15px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.deep-scan-summary h5 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #60a5fa;
}

.summary-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

.stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #60a5fa;
}

/* Responsive design */
@media (max-width: 1024px) {
  .scans-container {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .running-scans-monitor {
    padding: 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .controls {
    justify-content: center;
  }
}
