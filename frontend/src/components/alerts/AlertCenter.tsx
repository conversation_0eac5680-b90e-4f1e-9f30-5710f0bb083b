import { useEffect } from 'react';

interface AlertCenterProps {
  message: string;
  type?: 'success' | 'error';
  onClose: () => void;
}

export default function AlertCenter({ message, type = 'success', onClose }: AlertCenterProps) {
  useEffect(() => {
    const timer = setTimeout(onClose, 4000);
    return () => clearTimeout(timer);
  }, [onClose]);

  return (
    <div className={`fixed top-5 right-5 px-4 py-2 rounded shadow-lg z-50
      ${type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`}>
      {message}
    </div>
  );
}
