import React from 'react';
import { Shield } from 'lucide-react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ size = 'md', showText = true, className = '' }) => {
  const sizeClasses = {
    sm: {
      container: 'flex items-center space-x-2',
      icon: 'w-6 h-6',
      title: 'text-lg font-bold',
      subtitle: 'text-xs'
    },
    md: {
      container: 'flex items-center space-x-3',
      icon: 'w-8 h-8',
      title: 'text-xl font-bold',
      subtitle: 'text-sm'
    },
    lg: {
      container: 'flex items-center space-x-4',
      icon: 'w-12 h-12',
      title: 'text-3xl font-bold',
      subtitle: 'text-base'
    }
  };

  const classes = sizeClasses[size];

  return (
    <div className={`${showText ? classes.container : 'flex justify-center'} ${className}`}>
      {/* Logo Shield */}
      <div className="relative">
        <div className={`${classes.icon} bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg`}>
          <Shield className={`${size === 'sm' ? 'w-4 h-4' : size === 'md' ? 'w-5 h-5' : 'w-7 h-7'} text-white`} />
        </div>
        {/* Glow effect */}
        <div className={`absolute inset-0 ${classes.icon} bg-purple-500/30 rounded-lg blur-sm -z-10`}></div>
      </div>

      {/* Text */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${classes.title} text-white leading-tight`}>
            PICA
          </h1>
          <p className={`${classes.subtitle} text-purple-200 leading-tight`}>
            Automated Cybersecurity Platform
          </p>
        </div>
      )}
    </div>
  );
};

export default Logo;
