import React, { useState, useEffect } from 'react';
import { getRunningScans, getScanDetailsWithTools } from '../services/api';
import './RunningScansMonitor.css';

interface ToolStatus {
  status: 'pending' | 'running' | 'completed' | 'failed' | 'error' | 'unavailable';
  progress: number;
  start_time?: string;
  end_time?: string;
  error?: string;
  duration?: string;
}

interface ScanDetails {
  scan_id: string;
  category: string;
  scan_type: string;
  target: string;
  ports?: string;
  start_time: string;
  user_id: string;
  current_tool: string;
  tools: string[];
  tools_status?: { [key: string]: ToolStatus };
  status_summary?: { [key: string]: number };
  overall_progress?: number;
  current_duration?: string;
  current_duration_formatted?: string;
}

interface RunningScansData {
  running_scans: ScanDetails[];
  total_running: number;
  user_role: string;
}

const RunningScansMonitor: React.FC = () => {
  const [runningScans, setRunningScans] = useState<ScanDetails[]>([]);
  const [selectedScan, setSelectedScan] = useState<string | null>(null);
  const [scanDetails, setScanDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fonction pour charger les scans en cours
  const loadRunningScans = async () => {
    try {
      const data: RunningScansData = await getRunningScans();
      setRunningScans(data.running_scans);
      setError(null);
    } catch (err) {
      console.error('Error loading running scans:', err);
      setError('Failed to load running scans');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour charger les détails d'un scan
  const loadScanDetails = async (scanId: string) => {
    try {
      const details = await getScanDetailsWithTools(scanId);
      setScanDetails(details);
    } catch (err) {
      console.error('Error loading scan details:', err);
    }
  };

  // Effet pour charger les données initiales
  useEffect(() => {
    loadRunningScans();
  }, []);

  // Effet pour le rafraîchissement automatique
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadRunningScans();
      if (selectedScan) {
        loadScanDetails(selectedScan);
      }
    }, 5000); // Rafraîchir toutes les 5 secondes

    return () => clearInterval(interval);
  }, [autoRefresh, selectedScan]);

  // Fonction pour obtenir l'icône de la catégorie
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'network': return '🟦';
      case 'web': return '🟩';
      case 'vulnerability': return '🟪';
      case 'deep': return '🟥';
      default: return '🔍';
    }
  };

  // Fonction pour obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return '#3b82f6';
      case 'completed': return '#10b981';
      case 'failed': return '#ef4444';
      case 'error': return '#f59e0b';
      case 'unavailable': return '#6b7280';
      case 'pending': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  // Fonction pour formater le temps
  const formatTime = (isoString: string) => {
    return new Date(isoString).toLocaleTimeString();
  };

  if (loading) {
    return (
      <div className="running-scans-monitor">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading running scans...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="running-scans-monitor">
      <div className="header">
        <h2>🏃 Running Scans Monitor</h2>
        <div className="controls">
          <label className="auto-refresh">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
            />
            Auto-refresh (5s)
          </label>
          <button onClick={loadRunningScans} className="refresh-btn">
            🔄 Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      <div className="scans-container">
        <div className="scans-list">
          <h3>Running Scans ({runningScans.length})</h3>
          
          {runningScans.length === 0 ? (
            <div className="no-scans">
              <p>No scans currently running</p>
            </div>
          ) : (
            runningScans.map((scan) => (
              <div
                key={scan.scan_id}
                className={`scan-item ${selectedScan === scan.scan_id ? 'selected' : ''}`}
                onClick={() => {
                  setSelectedScan(scan.scan_id);
                  loadScanDetails(scan.scan_id);
                }}
              >
                <div className="scan-header">
                  <span className="category-icon">{getCategoryIcon(scan.category)}</span>
                  <div className="scan-info">
                    <div className="scan-title">
                      {scan.category.charAt(0).toUpperCase() + scan.category.slice(1)} Scan
                    </div>
                    <div className="scan-subtitle">
                      {scan.scan_type} • {scan.target}
                    </div>
                  </div>
                  <div className="scan-status">
                    <div className="current-tool">
                      🔧 {scan.current_tool}
                    </div>
                    <div className="duration">
                      ⏱️ {scan.current_duration_formatted || scan.current_duration}
                    </div>
                  </div>
                </div>

                {scan.overall_progress !== undefined && (
                  <div className="progress-bar">
                    <div
                      className="progress-fill"
                      style={{ width: `${scan.overall_progress}%` }}
                    ></div>
                    <span className="progress-text">{scan.overall_progress.toFixed(1)}%</span>
                  </div>
                )}

                {scan.tools_status && (
                  <div className="tools-status">
                    {Object.entries(scan.tools_status).map(([tool, status]) => (
                      <div
                        key={tool}
                        className="tool-status"
                        style={{ backgroundColor: getStatusColor(status.status) }}
                        title={`${tool}: ${status.status} (${status.progress}%)`}
                      >
                        {tool.charAt(0).toUpperCase()}
                      </div>
                    ))}
                  </div>
                )}

                {/* Show tool summary for deep scans */}
                {scan.category === 'deep' && scan.tools_status && (
                  <div className="deep-scan-mini-summary">
                    <span className="mini-stat">
                      ✅ {Object.values(scan.tools_status).filter((s: any) => s.status === 'completed').length}
                    </span>
                    <span className="mini-stat">
                      🔄 {Object.values(scan.tools_status).filter((s: any) => s.status === 'running').length}
                    </span>
                    <span className="mini-stat">
                      ⏳ {Object.values(scan.tools_status).filter((s: any) => s.status === 'pending').length}
                    </span>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        <div className="scan-details">
          {selectedScan && scanDetails ? (
            <div className="details-content">
              <h3>🔍 Scan Details</h3>
              
              <div className="details-grid">
                <div className="detail-item">
                  <label>Scan ID:</label>
                  <span>{scanDetails.scan_id}</span>
                </div>
                <div className="detail-item">
                  <label>Category:</label>
                  <span>{getCategoryIcon(scanDetails.category)} {scanDetails.category}</span>
                </div>
                <div className="detail-item">
                  <label>Type:</label>
                  <span>{scanDetails.scan_type}</span>
                </div>
                <div className="detail-item">
                  <label>Target:</label>
                  <span>{scanDetails.target}</span>
                </div>
                <div className="detail-item">
                  <label>Started:</label>
                  <span>{formatTime(scanDetails.start_time)}</span>
                </div>
                <div className="detail-item">
                  <label>Current Tool:</label>
                  <span>🔧 {scanDetails.current_tool}</span>
                </div>
                <div className="detail-item">
                  <label>Duration:</label>
                  <span>⏱️ {scanDetails.current_duration_formatted || scanDetails.current_duration}</span>
                </div>
              </div>

              {scanDetails.tools_status && (
                <div className="tools-detailed-status">
                  <h4>Tools Status ({Object.keys(scanDetails.tools_status).length} tools)</h4>

                  {/* Show all tools in a grid layout for better visibility */}
                  <div className="tools-grid">
                    {Object.entries(scanDetails.tools_status).map(([tool, status]: [string, any]) => (
                      <div key={tool} className="tool-card">
                        <div className="tool-header">
                          <span className="tool-name">{tool.toUpperCase()}</span>
                          <span
                            className="tool-status-badge"
                            style={{ backgroundColor: getStatusColor(status.status) }}
                          >
                            {status.status}
                          </span>
                        </div>

                        <div className="tool-progress">
                          <div className="progress-bar-small">
                            <div
                              className="progress-fill-small"
                              style={{ width: `${status.progress}%` }}
                            ></div>
                          </div>
                          <span className="progress-percent">{status.progress}%</span>
                        </div>

                        {status.start_time && (
                          <div className="tool-timing">
                            <span>Started: {formatTime(status.start_time)}</span>
                            {status.duration && <span>Duration: {status.duration}</span>}
                          </div>
                        )}

                        {status.error && (
                          <div className="tool-error">
                            ❌ {status.error}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Summary for deep scans */}
                  {scanDetails.category === 'deep' && (
                    <div className="deep-scan-summary">
                      <h5>Deep Scan Progress</h5>
                      <div className="summary-stats">
                        <div className="stat">
                          <span className="stat-label">Completed:</span>
                          <span className="stat-value">
                            {Object.values(scanDetails.tools_status).filter((s: any) => s.status === 'completed').length} / {Object.keys(scanDetails.tools_status).length}
                          </span>
                        </div>
                        <div className="stat">
                          <span className="stat-label">Running:</span>
                          <span className="stat-value">
                            {Object.values(scanDetails.tools_status).filter((s: any) => s.status === 'running').length}
                          </span>
                        </div>
                        <div className="stat">
                          <span className="stat-label">Pending:</span>
                          <span className="stat-value">
                            {Object.values(scanDetails.tools_status).filter((s: any) => s.status === 'pending').length}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="no-selection">
              <p>Select a running scan to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RunningScansMonitor;
