// 📁 src/hooks/useRegister.ts
import { authService } from '../services/authService';

type RegisterForm = {
  firstname: string;
  lastname: string;
  email: string;
  password: string;
  confirmPassword: string;
  birthdate: string;
  username: string;
  gender: string;
};

export const useRegister = () => {
  const register = async (form: RegisterForm): Promise<{ type: 'success' | 'error'; message: string }> => {
    try {
      const { firstname, lastname, email, password, birthdate, username, gender } = form;

      const formatDate = (dateStr: string) => {
        const date = new Date(dateStr);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      };

      await authService.register({
        first_name: firstname,
        last_name: lastname,
        email,
        password,
        username,
        date_of_birth: formatDate(birthdate),
        gender: gender || 'Not specified',
      });

      return { type: 'success', message: 'Registration successful. Please confirm your email address.' };
    } catch (err: any) {
      const message = err?.response?.data?.msg || 'Registration error';
      return { type: 'error', message };
    }
  };

  return { register };
};
